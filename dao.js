import db from './database.js';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const DB_PATH = path.join(__dirname, 'data.db');

// 保存数据库到文件的函数
const saveDatabase = () => {
  const data = db.export();
  const buffer = Buffer.from(data);
  fs.writeFileSync(DB_PATH, buffer);
};

// 通用的数据库操作辅助函数
const runQuery = (sql, params = []) => {
  const stmt = db.prepare(sql);
  stmt.run(...params);
  // 获取最后插入的行ID
  const lastIdStmt = db.prepare("SELECT last_insert_rowid() as id");
  const result = lastIdStmt.getAsObject();
  // 保存数据库到文件
  saveDatabase();
  return { id: result.id, changes: 1 };
};

const getQuery = (sql, params = []) => {
  const stmt = db.prepare(sql);
  stmt.bind(...params);
  if (stmt.step()) {
    return stmt.getAsObject();
  }
  stmt.free();
  return null;
};

const allQuery = (sql, params = []) => {
  const stmt = db.prepare(sql);
  stmt.bind(...params);
  const results = [];
  while (stmt.step()) {
    results.push(stmt.getAsObject());
  }
  stmt.free();
  return results;
};

// ==================== 位置相关操作 ====================

export const locationDAO = {
  // 获取所有位置
  getAll: () => {
    return allQuery('SELECT * FROM locations ORDER BY createdAt DESC');
  },

  // 根据ID获取位置
  getById: (id) => {
    return getQuery('SELECT * FROM locations WHERE id = ?', [id]);
  },

  // 获取默认位置
  getDefault: () => {
    return getQuery('SELECT * FROM locations WHERE isDefault = 1');
  },

  // 创建位置
  create: (location) => {
    const { name, longitude, latitude, height, country, city, stayDuration, isDefault } = location;
    const params = [
      name,
      longitude,
      latitude,
      height,
      country || '',
      city || '',
      stayDuration || 5.0,
      isDefault ? 1 : 0
    ];
    console.log('Creating location with params:', params);
    return runQuery(
      `INSERT INTO locations (name, longitude, latitude, height, country, city, stayDuration, isDefault)
       VALUES (?, ?, ?, ?, ?, ?, ?, ?)`,
      params
    );
  },

  // 更新位置
  update: (id, location) => {
    const { name, longitude, latitude, height, country, city, stayDuration, isDefault } = location;
    return runQuery(
      `UPDATE locations SET name = ?, longitude = ?, latitude = ?, height = ?,
       country = ?, city = ?, stayDuration = ?, isDefault = ? WHERE id = ?`,
      [
        name,
        longitude,
        latitude,
        height,
        country || '',
        city || '',
        stayDuration || 5.0,
        isDefault ? 1 : 0,
        id
      ]
    );
  },

  // 删除位置
  delete: (id) => {
    return runQuery('DELETE FROM locations WHERE id = ?', [id]);
  }
};

// ==================== 动画相关操作 ====================

export const animationDAO = {
  // 获取所有动画
  getAll: () => {
    return allQuery('SELECT * FROM animations ORDER BY createdAt DESC');
  },

  // 根据ID获取动画
  getById: (id) => {
    return getQuery('SELECT * FROM animations WHERE id = ?', [id]);
  },

  // 创建动画
  create: (animation) => {
    const { name, description, duration, isLoop } = animation;
    return runQuery(
      'INSERT INTO animations (name, description, duration, isLoop) VALUES (?, ?, ?, ?)',
      [name, description, duration || 10.0, isLoop ? 1 : 0]
    );
  },

  // 更新动画
  update: (id, animation) => {
    const { name, description, duration, isLoop } = animation;
    return runQuery(
      'UPDATE animations SET name = ?, description = ?, duration = ?, isLoop = ? WHERE id = ?',
      [name, description, duration, isLoop ? 1 : 0, id]
    );
  },

  // 删除动画
  delete: (id) => {
    return runQuery('DELETE FROM animations WHERE id = ?', [id]);
  }
};

// ==================== 动画路径相关操作 ====================

export const animationPathDAO = {
  // 获取所有路径
  getAll: () => {
    return allQuery('SELECT * FROM animation_paths ORDER BY animationId, sequence');
  },

  // 获取动画的所有路径
  getByAnimationId: (animationId) => {
    return allQuery('SELECT * FROM animation_paths WHERE animationId = ? ORDER BY sequence', [animationId]);
  },

  // 创建路径
  create: (path) => {
    const { animationId, locationId, sequence, selectedEventId, transitionDuration } = path;
    return runQuery(
      'INSERT INTO animation_paths (animationId, locationId, sequence, selectedEventId, transitionDuration) VALUES (?, ?, ?, ?, ?)',
      [animationId, locationId, sequence, selectedEventId || 0, transitionDuration || 3.0]
    );
  },

  // 更新路径
  update: (id, path) => {
    const { locationId, sequence, selectedEventId, transitionDuration } = path;
    return runQuery(
      'UPDATE animation_paths SET locationId = ?, sequence = ?, selectedEventId = ?, transitionDuration = ? WHERE id = ?',
      [locationId, sequence, selectedEventId, transitionDuration, id]
    );
  },

  // 删除路径
  delete: (id) => {
    return runQuery('DELETE FROM animation_paths WHERE id = ?', [id]);
  },

  // 删除动画的所有路径
  deleteByAnimationId: (animationId) => {
    return runQuery('DELETE FROM animation_paths WHERE animationId = ?', [animationId]);
  }
};

// ==================== 事件相关操作 ====================

export const eventDAO = {
  // 获取所有事件
  getAll: () => {
    return allQuery(`
      SELECT e.*, l.name as locationName, l.longitude, l.latitude, l.height, 
             l.country, l.city, l.stayDuration as locationStayDuration, l.isDefault
      FROM events e
      LEFT JOIN locations l ON e.locationId = l.id
      ORDER BY e.createdAt DESC
    `);
  },

  // 根据ID获取事件
  getById: (id) => {
    return getQuery('SELECT * FROM events WHERE id = ?', [id]);
  },

  // 根据位置ID获取事件
  getByLocationId: (locationId) => {
    return allQuery('SELECT * FROM events WHERE locationId = ? ORDER BY createdAt DESC', [locationId]);
  },

  // 创建事件
  create: (event) => {
    const { locationId, name, description, eventTime, imageUrl, videoUrl, content, 
            preDelay, displayDuration, postDelay, stayDuration, isActive, customIcon, iconColor } = event;
    return runQuery(
      `INSERT INTO events (locationId, name, description, eventTime, imageUrl, videoUrl, content,
                          preDelay, displayDuration, postDelay, stayDuration, isActive, customIcon, iconColor)
       VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
      [locationId, name, description, eventTime, imageUrl, videoUrl, content,
       preDelay || 0.0, displayDuration || 5.0, postDelay || 0.0, stayDuration || 5.0,
       isActive ? 1 : 0, customIcon, iconColor]
    );
  },

  // 更新事件
  update: (id, event) => {
    const { locationId, name, description, eventTime, imageUrl, videoUrl, content,
            preDelay, displayDuration, postDelay, stayDuration, isActive, customIcon, iconColor } = event;
    return runQuery(
      `UPDATE events SET locationId = ?, name = ?, description = ?, eventTime = ?, imageUrl = ?, 
                        videoUrl = ?, content = ?, preDelay = ?, displayDuration = ?, postDelay = ?,
                        stayDuration = ?, isActive = ?, customIcon = ?, iconColor = ? WHERE id = ?`,
      [locationId, name, description, eventTime, imageUrl, videoUrl, content,
       preDelay, displayDuration, postDelay, stayDuration, isActive ? 1 : 0, customIcon, iconColor, id]
    );
  },

  // 删除事件
  delete: (id) => {
    return runQuery('DELETE FROM events WHERE id = ?', [id]);
  }
};
