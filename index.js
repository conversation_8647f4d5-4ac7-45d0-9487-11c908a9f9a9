
import express from 'express';
import fetch from 'node-fetch';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import { initDatabase, insertInitialData, cleanDuplicateData } from './database.js';
import { locationDAO, animationDAO, animationPathDAO, eventDAO } from './dao.js';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const app = express();
const PORT = 8082;
const TDT_KEY = '38900fcb4ee1f4aba3598f88bf0a112b';
const CACHE_DIR = path.resolve(__dirname, 'tdt_cache')
const VIDEO_DIR = path.resolve(__dirname, 'video')
const IMAGE_DIR = path.resolve(__dirname, 'image');

// 数据库操作替代内存存储





// 天地图逆地理编码API
const getLocationInfo = async (longitude, latitude) => {
  console.log(`🌍 Getting location info for coordinates: ${longitude}, ${latitude}`);

  try {
    // 构建天地图逆地理编码API URL
    const postStr = JSON.stringify({
      lon: longitude,
      lat: latitude,
      ver: 1
    });

    const url = `http://api.tianditu.gov.cn/geocoder?postStr=${encodeURIComponent(postStr)}&type=geocode&tk=${TDT_KEY}`;
    console.log(`📡 Calling Tianditu API: ${url}`);

    const response = await fetch(url);
    console.log(`📊 Response status: ${response.status}`);

    if (!response.ok) {
      console.error(`❌ HTTP error! status: ${response.status}`);
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    console.log(`📋 API response data:`, JSON.stringify(data, null, 2));

    if (data.status === '0' && data.result && data.result.addressComponent) {
      const addressComponent = data.result.addressComponent;
      console.log(`📍 Address component:`, JSON.stringify(addressComponent, null, 2));

      const result = {
        country: addressComponent.nation || addressComponent.country || '未知',
        city: addressComponent.city || addressComponent.county || addressComponent.district || '未知'
      };

      console.log(`✅ Successfully parsed location info:`, result);
      return result;
    } else {
      console.warn(`⚠️ API returned unexpected format or error status: ${data.status}`);
      console.warn(`⚠️ Full response:`, JSON.stringify(data, null, 2));

      return {
        country: '未知',
        city: '未知'
      };
    }
  } catch (error) {
    console.error('❌ Failed to get location info from Tianditu API:', error);
    console.error('❌ Error details:', error.message);

    return {
      country: '未知',
      city: '未知'
    };
  }
};

if (!fs.existsSync(CACHE_DIR)) fs.mkdirSync(CACHE_DIR);

app.use((req, res, next) => {
  res.setHeader('Access-Control-Allow-Origin', '*'); // 允许所有来源
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, PATCH, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');

  // 处理预检请求
  if (req.method === 'OPTIONS') {
    res.status(200).end();
    return;
  }

  next();
});

// 添加JSON解析中间件
app.use(express.json());

// API接口：获取默认位置
app.get('/api/default-location', (req, res) => {
  try {
    const defaultLoc = locationDAO.getDefault();
    if (!defaultLoc) {
      // 如果没有默认位置，返回第一个位置
      const locations = locationDAO.getAll();
      const firstLocation = locations[0] || null;
      return res.json({
        success: true,
        data: firstLocation,
        message: 'Default location retrieved successfully'
      });
    }
    res.json({
      success: true,
      data: defaultLoc,
      message: 'Default location retrieved successfully'
    });
  } catch (error) {
    console.error('Error getting default location:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get default location'
    });
  }
});

// API接口：获取所有位置
app.get('/api/locations', (req, res) => {
  try {
    const locations = locationDAO.getAll();
    res.json({
      success: true,
      data: locations,
      message: 'Locations retrieved successfully'
    });
  } catch (error) {
    console.error('Error getting locations:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get locations'
    });
  }
});

// API接口：根据ID获取位置
app.get('/api/locations/:id', (req, res) => {
  try {
    const id = parseInt(req.params.id);
    const location = locationDAO.getById(id);

    if (!location) {
      return res.status(404).json({
        success: false,
        message: 'Location not found'
      });
    }

    res.json({
      success: true,
      data: location,
      message: 'Location retrieved successfully'
    });
  } catch (error) {
    console.error('Error getting location:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get location'
    });
  }
});

// API接口：创建新位置
app.post('/api/locations', async (req, res) => {
  const { name, longitude, latitude, height, country, city, stayDuration, isDefault } = req.body;

  // 验证输入参数
  if (!name || typeof name !== 'string' || name.trim().length === 0) {
    return res.status(400).json({
      success: false,
      message: 'Invalid name. Name is required and must be a non-empty string.'
    });
  }

  if (typeof longitude !== 'number' || longitude < -180 || longitude > 180) {
    return res.status(400).json({
      success: false,
      message: 'Invalid longitude. Must be a number between -180 and 180.'
    });
  }

  if (typeof latitude !== 'number' || latitude < -90 || latitude > 90) {
    return res.status(400).json({
      success: false,
      message: 'Invalid latitude. Must be a number between -90 and 90.'
    });
  }

  if (typeof height !== 'number' || height < 0) {
    return res.status(400).json({
      success: false,
      message: 'Invalid height. Must be a positive number.'
    });
  }

  try {
    // 检查名称是否已存在
    const existingLocations = locationDAO.getAll();
    if (existingLocations.some(loc => loc.name.toLowerCase() === name.toLowerCase())) {
      return res.status(400).json({
        success: false,
        message: 'Location name already exists'
      });
    }

    // 如果没有提供国家和城市信息，自动从天地图API获取
    let locationCountry = country;
    let locationCity = city;

    if (!country || !city) {
      try {
        const locationInfo = await getLocationInfo(longitude, latitude);
        locationCountry = country || locationInfo.country;
        locationCity = city || locationInfo.city;
      } catch (error) {
        console.error('Failed to get location info:', error);
        locationCountry = country || '未知';
        locationCity = city || '未知';
      }
    }

    // 如果设置为默认位置，取消其他位置的默认状态
    if (isDefault) {
      // 这里需要在数据库中更新其他位置的默认状态
      const allLocations = locationDAO.getAll();
      for (const loc of allLocations) {
        if (loc.isDefault) {
          locationDAO.update(loc.id, { ...loc, isDefault: false });
        }
      }
    }

    // 创建新位置
    const newLocationData = {
      name: name.trim(),
      longitude,
      latitude,
      height,
      country: locationCountry,
      city: locationCity,
      stayDuration: stayDuration || 5.0,
      isDefault: !!isDefault
    };

    const result = locationDAO.create(newLocationData);
    const newLocation = locationDAO.getById(result.id);

    res.status(201).json({
      success: true,
      data: newLocation,
      message: 'Location created successfully'
    });
  } catch (error) {
    console.error('Error creating location:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to create location'
    });
  }
});

// API接口：更新位置
app.put('/api/locations/:id', async (req, res) => {
  try {
    const id = parseInt(req.params.id);
    const { name, longitude, latitude, height, country, city, stayDuration, isDefault } = req.body;

    // 检查位置是否存在
    const existingLocation = locationDAO.getById(id);
    if (!existingLocation) {
      return res.status(404).json({
        success: false,
        message: 'Location not found'
      });
    }

    // 验证输入参数
    if (name !== undefined) {
      if (typeof name !== 'string' || name.trim().length === 0) {
        return res.status(400).json({
          success: false,
          message: 'Invalid name. Name must be a non-empty string.'
        });
      }

      // 检查名称是否已存在（排除当前位置）
      const allLocations = locationDAO.getAll();
      if (allLocations.some(loc => loc.id !== id && loc.name.toLowerCase() === name.toLowerCase())) {
        return res.status(400).json({
          success: false,
          message: 'Location name already exists'
        });
      }
    }

    if (longitude !== undefined && (typeof longitude !== 'number' || longitude < -180 || longitude > 180)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid longitude. Must be a number between -180 and 180.'
      });
    }

    if (latitude !== undefined && (typeof latitude !== 'number' || latitude < -90 || latitude > 90)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid latitude. Must be a number between -90 and 90.'
      });
    }

    if (height !== undefined && (typeof height !== 'number' || height < 0)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid height. Must be a positive number.'
      });
    }

    // 如果设置为默认位置，取消其他位置的默认状态
    if (isDefault) {
      const allLocations = locationDAO.getAll();
      for (const loc of allLocations) {
        if (loc.id !== id && loc.isDefault) {
          locationDAO.update(loc.id, { ...loc, isDefault: false });
        }
      }
    }

    // 准备更新数据
    const updateData = { ...existingLocation };
    let shouldUpdateLocationInfo = false;

    if (name !== undefined) updateData.name = name.trim();
    if (longitude !== undefined) {
      if (longitude !== existingLocation.longitude) shouldUpdateLocationInfo = true;
      updateData.longitude = longitude;
    }
    if (latitude !== undefined) {
      if (latitude !== existingLocation.latitude) shouldUpdateLocationInfo = true;
      updateData.latitude = latitude;
    }
    if (height !== undefined) updateData.height = height;
    if (country !== undefined) updateData.country = country;
    if (city !== undefined) updateData.city = city;
    if (stayDuration !== undefined) updateData.stayDuration = stayDuration;
    if (isDefault !== undefined) updateData.isDefault = !!isDefault;

    // 如果经纬度变化且没有明确提供国家城市信息，自动获取
    if (shouldUpdateLocationInfo && (country === undefined || city === undefined)) {
      try {
        const locationInfo = await getLocationInfo(updateData.longitude, updateData.latitude);
        if (country === undefined) updateData.country = locationInfo.country;
        if (city === undefined) updateData.city = locationInfo.city;
      } catch (error) {
        console.error('Failed to get location info during update:', error);
      }
    }

    // 更新数据库
    locationDAO.update(id, updateData);
    const updatedLocation = locationDAO.getById(id);

    res.json({
      success: true,
      data: updatedLocation,
      message: 'Location updated successfully'
    });
  } catch (error) {
    console.error('Error updating location:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update location'
    });
  }
});

// API接口：删除位置
app.delete('/api/locations/:id', (req, res) => {
  try {
    const id = parseInt(req.params.id);
    const location = locationDAO.getById(id);

    if (!location) {
      return res.status(404).json({
        success: false,
        message: 'Location not found'
      });
    }

    // 如果删除的是默认位置，将第一个剩余位置设为默认
    if (location.isDefault) {
      const allLocations = locationDAO.getAll();
      const remainingLocations = allLocations.filter(loc => loc.id !== id);
      if (remainingLocations.length > 0) {
        locationDAO.update(remainingLocations[0].id, { ...remainingLocations[0], isDefault: true });
      }
    }

    // 删除位置
    locationDAO.delete(id);

    res.json({
      success: true,
      data: location,
      message: 'Location deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting location:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to delete location'
    });
  }
});

// API接口：设置默认位置
app.patch('/api/locations/:id/set-default', (req, res) => {
  try {
    const id = parseInt(req.params.id);
    const location = locationDAO.getById(id);

    if (!location) {
      return res.status(404).json({
        success: false,
        message: 'Location not found'
      });
    }

    // 取消所有位置的默认状态
    const allLocations = locationDAO.getAll();
    for (const loc of allLocations) {
      if (loc.isDefault) {
        locationDAO.update(loc.id, { ...loc, isDefault: false });
      }
    }

    // 设置当前位置为默认
    locationDAO.update(id, { ...location, isDefault: true });
    const updatedLocation = locationDAO.getById(id);

    res.json({
      success: true,
      data: updatedLocation,
      message: 'Default location set successfully'
    });
  } catch (error) {
    console.error('Error setting default location:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to set default location'
    });
  }
});

// ==================== 动画管理 API ====================

// 获取动画详情（包含路径信息）
const getAnimationWithPaths = (animationId) => {
  const animation = animationDAO.getById(animationId);
  if (!animation) return null;

  const paths = animationPathDAO.getByAnimationId(animationId)
    .sort((a, b) => a.sequence - b.sequence)
    .map(path => {
      const location = locationDAO.getById(path.locationId);
      return {
        ...path,
        location: location || null
      };
    });

  return {
    ...animation,
    paths
  };
};

// API接口：获取所有动画（支持分页、排序、搜索）
app.get('/api/animations', (req, res) => {
  try {
    const {
      page = 1,
      limit = 10,
      sortBy = 'createdAt',
      sortOrder = 'desc',
      search = '',
      isLoop
    } = req.query;

    let filteredAnimations = animationDAO.getAll();

    // 搜索过滤
    if (search) {
      const searchTerm = search.toLowerCase();
      filteredAnimations = filteredAnimations.filter(animation =>
        animation.name.toLowerCase().includes(searchTerm) ||
        (animation.description && animation.description.toLowerCase().includes(searchTerm))
      );
    }

    // 循环状态过滤
    if (isLoop !== undefined) {
      const loopFilter = isLoop === 'true';
      filteredAnimations = filteredAnimations.filter(animation => animation.isLoop === loopFilter);
    }

    // 排序
    filteredAnimations.sort((a, b) => {
      let aValue = a[sortBy];
      let bValue = b[sortBy];

      if (sortBy === 'pathCount') {
        aValue = animationPathDAO.getByAnimationId(a.id).length;
        bValue = animationPathDAO.getByAnimationId(b.id).length;
      }

      if (typeof aValue === 'string') {
        aValue = aValue.toLowerCase();
        bValue = bValue.toLowerCase();
      }

      if (sortOrder === 'desc') {
        return bValue > aValue ? 1 : -1;
      } else {
        return aValue > bValue ? 1 : -1;
      }
    });

    // 分页
    const pageNum = parseInt(page);
    const limitNum = parseInt(limit);
    const startIndex = (pageNum - 1) * limitNum;
    const endIndex = startIndex + limitNum;
    const paginatedAnimations = filteredAnimations.slice(startIndex, endIndex);

    // 获取完整动画数据
    const animationsWithPaths = paginatedAnimations.map(animation =>
      getAnimationWithPaths(animation.id)
    );

    res.json({
      success: true,
      data: animationsWithPaths,
      pagination: {
        page: pageNum,
        limit: limitNum,
        total: filteredAnimations.length,
        totalPages: Math.ceil(filteredAnimations.length / limitNum),
        hasNext: endIndex < filteredAnimations.length,
        hasPrev: pageNum > 1
      },
      message: 'Animations retrieved successfully'
    });
  } catch (error) {
    console.error('Error getting animations:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get animations'
    });
  }
});

// API接口：获取快捷动画列表（优化版本，只返回必要信息）
app.get('/api/animations/quick', (req, res) => {
  try {
    const limit = parseInt(req.query.limit) || 4; // 默认返回4个
    const allAnimations = animationDAO.getAll();
    const quickAnimations = allAnimations
      .slice(0, limit)
      .map(animation => {
        const paths = animationPathDAO.getByAnimationId(animation.id);
        return {
          id: animation.id,
          name: animation.name,
          duration: animation.duration,
          pathCount: paths.length,
          isLoop: animation.isLoop,
          createdAt: animation.createdAt
        };
      });

    res.json({
      success: true,
      data: quickAnimations,
      message: 'Quick animations retrieved successfully'
    });
  } catch (error) {
    console.error('Error getting quick animations:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get quick animations'
    });
  }
});

// API接口：获取动画统计信息（必须在 :id 路由之前）
app.get('/api/animations/stats', (req, res) => {
  try {
    const allAnimations = animationDAO.getAll();
    const allPaths = animationPathDAO.getAll();

    const totalAnimations = allAnimations.length;
    const loopAnimations = allAnimations.filter(anim => anim.isLoop).length;
    const totalPaths = allPaths.length;

    // 计算平均路径数
    const avgPaths = totalAnimations > 0 ? (totalPaths / totalAnimations).toFixed(1) : 0;

    // 计算平均时长
    const avgDuration = totalAnimations > 0 ?
      (allAnimations.reduce((sum, anim) => sum + anim.duration, 0) / totalAnimations).toFixed(1) : 0;

    // 最近创建的动画
    const recentAnimations = allAnimations
      .sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())
      .slice(0, 5)
      .map(anim => ({
        id: anim.id,
        name: anim.name,
        createdAt: anim.createdAt
      }));

    res.json({
      success: true,
      data: {
        total: totalAnimations,
        loopCount: loopAnimations,
        nonLoopCount: totalAnimations - loopAnimations,
        totalPaths,
        avgPaths: parseFloat(avgPaths),
        avgDuration: parseFloat(avgDuration),
        recent: recentAnimations
      },
      message: 'Animation statistics retrieved successfully'
    });
  } catch (error) {
    console.error('Error getting animation statistics:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get animation statistics'
    });
  }
});

// API接口：根据ID获取动画
app.get('/api/animations/:id', (req, res) => {
  const id = parseInt(req.params.id);
  const animation = getAnimationWithPaths(id);

  if (!animation) {
    return res.status(404).json({
      success: false,
      message: 'Animation not found'
    });
  }

  res.json({
    success: true,
    data: animation,
    message: 'Animation retrieved successfully'
  });
});

// API接口：创建新动画
app.post('/api/animations', (req, res) => {
  try {
    const { name, description, duration, isLoop, paths } = req.body;

    // 验证输入参数
    if (!name || typeof name !== 'string' || name.trim().length === 0) {
      return res.status(400).json({
        success: false,
        message: 'Invalid name. Name is required and must be a non-empty string.'
      });
    }

    if (duration !== undefined && (typeof duration !== 'number' || duration <= 0)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid duration. Must be a positive number.'
      });
    }

    if (paths && (!Array.isArray(paths) || paths.length < 2)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid paths. Must be an array with at least 2 locations.'
      });
    }

    // 检查名称是否已存在
    const existingAnimations = animationDAO.getAll();
    if (existingAnimations.some(anim => anim.name.toLowerCase() === name.toLowerCase())) {
      return res.status(400).json({
        success: false,
        message: 'Animation name already exists'
      });
    }

    // 创建新动画
    const newAnimationData = {
      name: name.trim(),
      description: description || '',
      duration: duration || 3.0,
      isLoop: !!isLoop ? 1 : 0
    };

    const result = animationDAO.create(newAnimationData);
    const newAnimation = animationDAO.getById(result.id);

    // 创建动画路径
    if (paths && Array.isArray(paths)) {
      paths.forEach((path, index) => {
        if (path.locationId && locationDAO.getById(path.locationId)) {
          animationPathDAO.create({
            animationId: newAnimation.id,
            locationId: path.locationId,
            sequence: index + 1,
            selectedEventId: path.selectedEventId || 0,
            transitionDuration: path.transitionDuration || 3.0
          });
        }
      });
    }

    const animationWithPaths = getAnimationWithPaths(newAnimation.id);

    res.status(201).json({
      success: true,
      data: animationWithPaths,
      message: 'Animation created successfully'
    });
  } catch (error) {
    console.error('Error creating animation:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to create animation'
    });
  }
});

// API接口：更新动画
app.put('/api/animations/:id', (req, res) => {
  try {
    const id = parseInt(req.params.id);
    const { name, description, duration, isLoop, paths } = req.body;

    const existingAnimation = animationDAO.getById(id);
    if (!existingAnimation) {
      return res.status(404).json({
        success: false,
        message: 'Animation not found'
      });
    }

    // 验证输入参数
    if (name !== undefined) {
      if (typeof name !== 'string' || name.trim().length === 0) {
        return res.status(400).json({
          success: false,
          message: 'Invalid name. Name must be a non-empty string.'
        });
      }

      // 检查名称是否已存在（排除当前动画）
      const allAnimations = animationDAO.getAll();
      if (allAnimations.some(anim => anim.id !== id && anim.name.toLowerCase() === name.toLowerCase())) {
        return res.status(400).json({
          success: false,
          message: 'Animation name already exists'
        });
      }
    }

    if (duration !== undefined && (typeof duration !== 'number' || duration <= 0)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid duration. Must be a positive number.'
      });
    }

    if (paths && (!Array.isArray(paths) || paths.length < 2)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid paths. Must be an array with at least 2 locations.'
      });
    }

    // 准备更新数据
    const updateData = { ...existingAnimation };
    if (name !== undefined) updateData.name = name.trim();
    if (description !== undefined) updateData.description = description;
    if (duration !== undefined) updateData.duration = duration;
    if (isLoop !== undefined) updateData.isLoop = !!isLoop ? 1 : 0;

    // 更新动画基本信息
    animationDAO.update(id, updateData);

    // 更新动画路径
    if (paths && Array.isArray(paths)) {
      // 删除旧路径
      const oldPaths = animationPathDAO.getByAnimationId(id);
      oldPaths.forEach(path => {
        animationPathDAO.delete(path.id);
      });

      // 创建新路径
      paths.forEach((path, index) => {
        if (path.locationId && locationDAO.getById(path.locationId)) {
          animationPathDAO.create({
            animationId: id,
            locationId: path.locationId,
            sequence: index + 1,
            selectedEventId: path.selectedEventId || 0,
            transitionDuration: path.transitionDuration || 3.0
          });
        }
      });
    }

    const animationWithPaths = getAnimationWithPaths(id);

    res.json({
      success: true,
      data: animationWithPaths,
      message: 'Animation updated successfully'
    });
  } catch (error) {
    console.error('Error updating animation:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update animation'
    });
  }
});

// API接口：删除动画
app.delete('/api/animations/:id', (req, res) => {
  try {
    const id = parseInt(req.params.id);
    const animation = animationDAO.getById(id);

    if (!animation) {
      return res.status(404).json({
        success: false,
        message: 'Animation not found'
      });
    }

    // 删除动画路径（由于外键约束，会自动级联删除）
    const paths = animationPathDAO.getByAnimationId(id);
    paths.forEach(path => {
      animationPathDAO.delete(path.id);
    });

    // 删除动画
    animationDAO.delete(id);

    res.json({
      success: true,
      data: animation,
      message: 'Animation deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting animation:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to delete animation'
    });
  }
});

// API接口：批量删除动画
app.delete('/api/animations/batch', (req, res) => {
  const { ids } = req.body;

  if (!Array.isArray(ids) || ids.length === 0) {
    return res.status(400).json({
      success: false,
      message: 'Invalid ids. Must be a non-empty array.'
    });
  }

  const deletedAnimations = [];
  const notFoundIds = [];

  ids.forEach(id => {
    const animation = animationDAO.getById(id);
    if (animation) {
      deletedAnimations.push(animation);

      // 删除动画路径
      const paths = animationPathDAO.getByAnimationId(id);
      paths.forEach(path => {
        animationPathDAO.delete(path.id);
      });

      // 删除动画
      animationDAO.delete(id);
    } else {
      notFoundIds.push(id);
    }
  });

  res.json({
    success: true,
    data: {
      deleted: deletedAnimations,
      notFound: notFoundIds,
      deletedCount: deletedAnimations.length
    },
    message: `Successfully deleted ${deletedAnimations.length} animations`
  });
});

// API接口：复制动画
app.post('/api/animations/:id/duplicate', (req, res) => {
  const id = parseInt(req.params.id);
  const { name } = req.body;

  const sourceAnimation = getAnimationWithPaths(id);
  if (!sourceAnimation) {
    return res.status(404).json({
      success: false,
      message: 'Source animation not found'
    });
  }

  // 生成新名称
  const newName = name || `${sourceAnimation.name} (副本)`;

  // 检查名称是否已存在
  const allAnimations = animationDAO.getAll();
  if (allAnimations.some(anim => anim.name.toLowerCase() === newName.toLowerCase())) {
    return res.status(400).json({
      success: false,
      message: 'Animation name already exists'
    });
  }

  // 创建新动画
  const newAnimationData = {
    name: newName,
    description: sourceAnimation.description || '',
    duration: sourceAnimation.duration,
    isLoop: sourceAnimation.isLoop ? 1 : 0
  };

  const result = animationDAO.create(newAnimationData);
  const newAnimation = animationDAO.getById(result.id);

  // 复制动画路径
  if (sourceAnimation.paths && sourceAnimation.paths.length > 0) {
    sourceAnimation.paths.forEach(path => {
      animationPathDAO.create({
        animationId: newAnimation.id,
        locationId: path.locationId,
        sequence: path.sequence,
        selectedEventId: path.selectedEventId || 0,
        transitionDuration: path.transitionDuration || 3.0
      });
    });
  }

  const animationWithPaths = getAnimationWithPaths(newAnimation.id);

  res.status(201).json({
    success: true,
    data: animationWithPaths,
    message: 'Animation duplicated successfully'
  });
});



// ==================== 事件管理 API ====================

// 获取事件详情（包含位置信息）
const getEventWithLocation = (eventId) => {
  const event = eventDAO.getById(eventId);
  if (!event) return null;

  const location = locationDAO.getById(event.locationId);
  return {
    ...event,
    location: location || null
  };
};

// API接口：获取所有事件（支持分页、排序、搜索）
app.get('/api/events', (req, res) => {
  try {
    const {
      page = 1,
      limit = 10,
      sortBy = 'createdAt',
      sortOrder = 'desc',
      search = '',
      locationId,
      isActive
    } = req.query;

    let filteredEvents = eventDAO.getAll();

  // 搜索过滤
  if (search) {
    const searchTerm = search.toLowerCase();
    filteredEvents = filteredEvents.filter(event =>
      event.name.toLowerCase().includes(searchTerm) ||
      (event.description && event.description.toLowerCase().includes(searchTerm)) ||
      (event.content && event.content.toLowerCase().includes(searchTerm))
    );
  }

  // 位置过滤
  if (locationId !== undefined) {
    const locId = parseInt(locationId);
    filteredEvents = filteredEvents.filter(event => event.locationId === locId);
  }

  // 状态过滤
  if (isActive !== undefined) {
    const activeFilter = isActive === 'true';
    filteredEvents = filteredEvents.filter(event => event.isActive === activeFilter);
  }

  // 排序
  filteredEvents.sort((a, b) => {
    let aValue = a[sortBy];
    let bValue = b[sortBy];

    if (sortBy === 'locationName') {
      const aLocation = locationDAO.getById(a.locationId);
      const bLocation = locationDAO.getById(b.locationId);
      aValue = aLocation ? aLocation.name.toLowerCase() : '';
      bValue = bLocation ? bLocation.name.toLowerCase() : '';
    }

    if (typeof aValue === 'string') {
      aValue = aValue.toLowerCase();
      bValue = bValue.toLowerCase();
    }

    if (sortOrder === 'desc') {
      return bValue > aValue ? 1 : -1;
    } else {
      return aValue > bValue ? 1 : -1;
    }
  });

  // 分页
  const pageNum = parseInt(page);
  const limitNum = parseInt(limit);
  const startIndex = (pageNum - 1) * limitNum;
  const endIndex = startIndex + limitNum;
  const paginatedEvents = filteredEvents.slice(startIndex, endIndex);

  // 获取完整事件数据
  const eventsWithLocation = paginatedEvents.map(event =>
    getEventWithLocation(event.id)
  );

  res.json({
    success: true,
    data: eventsWithLocation,
    pagination: {
      page: pageNum,
      limit: limitNum,
      total: filteredEvents.length,
      totalPages: Math.ceil(filteredEvents.length / limitNum),
      hasNext: endIndex < filteredEvents.length,
      hasPrev: pageNum > 1
    },
    message: 'Events retrieved successfully'
  });
  } catch (error) {
    console.error('Error getting events:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get events'
    });
  }
});

// API接口：获取快捷事件列表（优化版本，只返回必要信息）
app.get('/api/events/quick', (req, res) => {
  try {
    const limit = parseInt(req.query.limit) || 4; // 默认返回4个
    const allEvents = eventDAO.getAll();
    const quickEvents = allEvents
      .slice(0, limit)
      .map(event => {
        const location = locationDAO.getById(event.locationId);
        return {
          id: event.id,
          name: event.name,
          eventTime: event.eventTime,
          isActive: event.isActive,
          location: location ? {
            id: location.id,
            name: location.name,
            longitude: location.longitude,
            latitude: location.latitude,
            height: location.height
          } : null,
          createdAt: event.createdAt
        };
      });

    res.json({
      success: true,
      data: quickEvents,
      message: 'Quick events retrieved successfully'
    });
  } catch (error) {
    console.error('Error getting quick events:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get quick events'
    });
  }
});

// API接口：根据位置ID获取事件
app.get('/api/locations/:locationId/events', (req, res) => {
  try {
    const locationId = parseInt(req.params.locationId);
    const locationEvents = eventDAO.getByLocationId(locationId)
      .map(event => getEventWithLocation(event.id));

    res.json({
      success: true,
      data: locationEvents,
      message: 'Location events retrieved successfully'
    });
  } catch (error) {
    console.error('Error getting location events:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get location events'
    });
  }
});

// API接口：获取事件统计信息（必须在 :id 路由之前）
app.get('/api/events/stats', (req, res) => {
  try {
    const allEvents = eventDAO.getAll();
    const totalEvents = allEvents.length;
    const activeEvents = allEvents.filter(evt => evt.isActive).length;
    const inactiveEvents = totalEvents - activeEvents;

    // 按位置统计
    const locationStats = {};
    allEvents.forEach(event => {
      const location = locationDAO.getById(event.locationId);
      const locationName = location ? location.name : '未知位置';
      locationStats[locationName] = (locationStats[locationName] || 0) + 1;
    });

    // 计算平均时长
    const avgDisplayDuration = totalEvents > 0 ?
      (allEvents.reduce((sum, evt) => sum + evt.displayDuration, 0) / totalEvents).toFixed(1) : 0;

    const avgStayDuration = totalEvents > 0 ?
      (allEvents.reduce((sum, evt) => sum + evt.stayDuration, 0) / totalEvents).toFixed(1) : 0;

    // 媒体内容统计
    const withImage = allEvents.filter(evt => evt.imageUrl && evt.imageUrl.trim()).length;
    const withVideo = allEvents.filter(evt => evt.videoUrl && evt.videoUrl.trim()).length;
    const withBoth = allEvents.filter(evt =>
      evt.imageUrl && evt.imageUrl.trim() && evt.videoUrl && evt.videoUrl.trim()
    ).length;

    // 最近创建的事件
    const recentEvents = allEvents
      .sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())
      .slice(0, 5)
      .map(evt => {
        const location = locationDAO.getById(evt.locationId);
        return {
          id: evt.id,
          name: evt.name,
          locationName: location?.name || '未知位置',
          createdAt: evt.createdAt
        };
      });

    res.json({
      success: true,
      data: {
        total: totalEvents,
        activeCount: activeEvents,
        inactiveCount: inactiveEvents,
        locationStats,
        avgDisplayDuration: parseFloat(avgDisplayDuration),
        avgStayDuration: parseFloat(avgStayDuration),
        mediaStats: {
          withImage,
          withVideo,
          withBoth,
          withoutMedia: totalEvents - withImage - withVideo + withBoth
        },
        recent: recentEvents
      },
      message: 'Event statistics retrieved successfully'
    });
  } catch (error) {
    console.error('Error getting event statistics:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get event statistics'
    });
  }
});

// API接口：根据ID获取事件
app.get('/api/events/:id', (req, res) => {
  try {
    const id = parseInt(req.params.id);
    const event = getEventWithLocation(id);

    if (!event) {
      return res.status(404).json({
        success: false,
        message: 'Event not found'
      });
    }

    res.json({
      success: true,
      data: event,
      message: 'Event retrieved successfully'
    });
  } catch (error) {
    console.error('Error getting event:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get event'
    });
  }
});

// API接口：创建新事件
app.post('/api/events', (req, res) => {
  try {
    const { locationId, name, description, eventTime, imageUrl, videoUrl, content, preDelay, displayDuration, postDelay, stayDuration, isActive, customIcon, iconColor } = req.body;

    // 验证输入参数
    if (!locationId || typeof locationId !== 'number') {
      return res.status(400).json({
        success: false,
        message: 'Invalid locationId. Must be a valid number.'
      });
    }

    if (!name || typeof name !== 'string' || name.trim().length === 0) {
      return res.status(400).json({
        success: false,
        message: 'Invalid name. Name is required and must be a non-empty string.'
      });
    }

    // 检查位置是否存在
    const location = locationDAO.getById(locationId);
    if (!location) {
      return res.status(400).json({
        success: false,
        message: 'Location not found'
      });
    }

    // 创建新事件
    const newEventData = {
      locationId,
      name: name.trim(),
      description: description || '',
      eventTime: eventTime || new Date().toISOString(),
      imageUrl: imageUrl || '',
      videoUrl: videoUrl || '',
      content: content || '',
      preDelay: preDelay || 0.0,
      displayDuration: displayDuration || 10.0,
      postDelay: postDelay || 0.0,
      stayDuration: stayDuration || 5.0,
      isActive: isActive !== undefined ? (!!isActive ? 1 : 0) : 1,
      customIcon: customIcon || '📅',
      iconColor: iconColor || '#9C27B0'
    };

    const result = eventDAO.create(newEventData);
    const eventWithLocation = getEventWithLocation(result.id);

    res.status(201).json({
      success: true,
      data: eventWithLocation,
      message: 'Event created successfully'
    });
  } catch (error) {
    console.error('Error creating event:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to create event'
    });
  }
});

// API接口：更新事件
app.put('/api/events/:id', (req, res) => {
  try {
    const id = parseInt(req.params.id);
    const { locationId, name, description, eventTime, imageUrl, videoUrl, content, preDelay, displayDuration, postDelay, stayDuration, isActive, customIcon, iconColor } = req.body;

    const existingEvent = eventDAO.getById(id);
    if (!existingEvent) {
      return res.status(404).json({
        success: false,
        message: 'Event not found'
      });
    }

    // 验证输入参数
    if (locationId !== undefined) {
      if (typeof locationId !== 'number') {
        return res.status(400).json({
          success: false,
          message: 'Invalid locationId. Must be a valid number.'
        });
      }

      // 检查位置是否存在
      const location = locationDAO.getById(locationId);
      if (!location) {
        return res.status(400).json({
          success: false,
          message: 'Location not found'
        });
      }
    }

    if (name !== undefined && (typeof name !== 'string' || name.trim().length === 0)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid name. Name must be a non-empty string.'
      });
    }

    // 准备更新数据
    const updateData = { ...existingEvent };
    if (locationId !== undefined) updateData.locationId = locationId;
    if (name !== undefined) updateData.name = name.trim();
    if (description !== undefined) updateData.description = description;
    if (eventTime !== undefined) updateData.eventTime = eventTime;
    if (imageUrl !== undefined) updateData.imageUrl = imageUrl;
    if (videoUrl !== undefined) updateData.videoUrl = videoUrl;
    if (content !== undefined) updateData.content = content;
    if (preDelay !== undefined) updateData.preDelay = preDelay;
    if (displayDuration !== undefined) updateData.displayDuration = displayDuration;
    if (postDelay !== undefined) updateData.postDelay = postDelay;
    if (stayDuration !== undefined) updateData.stayDuration = stayDuration;
    if (isActive !== undefined) updateData.isActive = !!isActive ? 1 : 0;
    if (customIcon !== undefined) updateData.customIcon = customIcon;
    if (iconColor !== undefined) updateData.iconColor = iconColor;

    // 更新数据库
    eventDAO.update(id, updateData);
    const eventWithLocation = getEventWithLocation(id);

    res.json({
      success: true,
      data: eventWithLocation,
      message: 'Event updated successfully'
    });
  } catch (error) {
    console.error('Error updating event:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update event'
    });
  }
});

// API接口：删除事件
app.delete('/api/events/:id', (req, res) => {
  try {
    const id = parseInt(req.params.id);
    const event = eventDAO.getById(id);

    if (!event) {
      return res.status(404).json({
        success: false,
        message: 'Event not found'
      });
    }

    eventDAO.delete(id);

    res.json({
      success: true,
      data: event,
      message: 'Event deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting event:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to delete event'
    });
  }
});

// API接口：批量删除事件
app.delete('/api/events/batch', (req, res) => {
  const { ids } = req.body;

  if (!Array.isArray(ids) || ids.length === 0) {
    return res.status(400).json({
      success: false,
      message: 'Invalid ids. Must be a non-empty array.'
    });
  }

  const deletedEvents = [];
  const notFoundIds = [];

  ids.forEach(id => {
    const event = eventDAO.getById(id);
    if (event) {
      deletedEvents.push(event);
      eventDAO.delete(id);
    } else {
      notFoundIds.push(id);
    }
  });

  res.json({
    success: true,
    data: {
      deleted: deletedEvents,
      notFound: notFoundIds,
      deletedCount: deletedEvents.length
    },
    message: `Successfully deleted ${deletedEvents.length} events`
  });
});

// API接口：复制事件
app.post('/api/events/:id/duplicate', (req, res) => {
  const id = parseInt(req.params.id);
  const { name, locationId } = req.body;

  const sourceEvent = events.find(evt => evt.id === id);
  if (!sourceEvent) {
    return res.status(404).json({
      success: false,
      message: 'Source event not found'
    });
  }

  // 生成新名称
  const newName = name || `${sourceEvent.name} (副本)`;

  // 检查名称是否已存在
  if (events.some(evt => evt.name.toLowerCase() === newName.toLowerCase())) {
    return res.status(400).json({
      success: false,
      message: 'Event name already exists'
    });
  }

  // 验证目标位置
  const targetLocationId = locationId || sourceEvent.locationId;
  const targetLocation = locationDAO.getById(targetLocationId);
  if (!targetLocation) {
    return res.status(400).json({
      success: false,
      message: 'Target location not found'
    });
  }

  // 创建新事件
  const newEvent = {
    id: nextEventId++,
    locationId: targetLocationId,
    name: newName,
    description: sourceEvent.description || '',
    eventTime: new Date().toISOString(), // 使用当前时间
    imageUrl: sourceEvent.imageUrl || '',
    videoUrl: sourceEvent.videoUrl || '',
    content: sourceEvent.content || '',
    preDelay: sourceEvent.preDelay,
    displayDuration: sourceEvent.displayDuration,
    postDelay: sourceEvent.postDelay,
    stayDuration: sourceEvent.stayDuration,
    isActive: sourceEvent.isActive,
    createdAt: new Date().toISOString()
  };

  events.push(newEvent);

  const eventWithLocation = getEventWithLocation(newEvent.id);

  res.status(201).json({
    success: true,
    data: eventWithLocation,
    message: 'Event duplicated successfully'
  });
});



const cacheSend = async (url, filePath, res) => {
    res.setHeader('Access-Control-Allow-Origin', '*'); // 允许所有来源

  if (fs.existsSync(filePath)) {
    res.sendFile(filePath);
  } else {
    try {
      const tile = await fetch(url);
      const buffer = await tile.buffer();
      fs.mkdirSync(path.dirname(filePath), { recursive: true });
      fs.writeFileSync(filePath, buffer);
            res.type('png').send(buffer);

    }catch (err) {
      console.error(`Error fetching ${url}:`, err);
      res.status(500).send('Failed to fetch tile');
    }
  }
}

// 叠加影像服务
app.get('/tdt/:s/DataServer', async (req, res) => {
  const { s } = req.params;
  const { T, l, x, y } = req.query;

  const filePath = path.join(CACHE_DIR, 'DataServer',  T, x, y, `${l}.png`);
  const url = `https://t${s}.tianditu.gov.cn/DataServer?T=${T}&x=${x}&y=${y}&l=${l}&tk=${TDT_KEY}`;

  cacheSend(url, filePath, res);

})


// 地形服务
app.get('/tdt/:s/mapservice/swdx', async (req, res) => {
  const { s } = req.params;
  const { T, l, x, y } = req.query;
  if (!T || !l || !x || !y) {
    return res.status(400).send('Missing required query parameters: T, l, x, y');
  }
  const filePath = path.join(CACHE_DIR, 'swdx', T, x, y, `${l}.png`);
  const url = `https://t${s}.tianditu.gov.cn/mapservice/swdx?T=${T}&x=${x}&y=${y}&l=${l}&tk=${TDT_KEY}`;

  cacheSend(url, filePath, res);
})

// 三维地名服务，使用wtfs服务
app.get('/tdt/:s/mapservice/GetTiles', async (req, res) => {
  const { s } = req.params;
  const { lxys } = req.query;
  if (!lxys) {
    
    return res.status(400).send('Missing required query parameters: T, l, x, y');
  }
  const filePath = path.join(CACHE_DIR, 'GetTiles', `${lxys}.png`);
  const url = `https://t${s}.tianditu.gov.cn/GetTiles?lxys=${lxys}&VERSION=1.0.0&tk=${TDT_KEY}`;

  cacheSend(url, filePath, res);
});

// 三维图标服务
app.get('/tdt/:s/mapservice/GetIcon', async (req, res) => {
  const { s } = req.params;
  const { id } = req.query;
  if (!id) {
    return res.status(400).send('Missing required query parameters: lxys');
  }
  const filePath = path.join(CACHE_DIR, 'GetIcon', `${id}.png`);
  const url = `https://t${s}.tianditu.gov.cn/GetIcon?id=${id}&tk=${TDT_KEY}`;

  cacheSend(url, filePath, res);
});

// ==================== 媒体代理服务 ====================

// 确保媒体目录存在
if (!fs.existsSync(VIDEO_DIR)) {
  fs.mkdirSync(VIDEO_DIR, { recursive: true });
  console.log(`📁 Created video directory: ${VIDEO_DIR}`);
}

if (!fs.existsSync(IMAGE_DIR)) {
  fs.mkdirSync(IMAGE_DIR, { recursive: true });
  console.log(`📁 Created image directory: ${IMAGE_DIR}`);
}

// 视频代理接口
app.get('/api/media/video/:filename', (req, res) => {
  const filename = req.params.filename;
  const videoPath = path.join(VIDEO_DIR, filename);

  console.log(`🎥 Video request: ${filename}`);
  console.log(`📂 Video path: ${videoPath}`);

  // 检查文件是否存在
  if (!fs.existsSync(videoPath)) {
    console.log(`❌ Video file not found: ${videoPath}`);
    return res.status(404).json({
      success: false,
      message: `Video file not found: ${filename}`
    });
  }

  try {
    // 获取文件信息
    const stat = fs.statSync(videoPath);
    const fileSize = stat.size;
    const range = req.headers.range;

    console.log(`📊 Video file size: ${fileSize} bytes`);
    console.log(`🎯 Range request: ${range || 'none'}`);

    // 设置正确的Content-Type
    const ext = path.extname(filename).toLowerCase();
    let contentType = 'video/mp4';

    switch (ext) {
      case '.mp4':
        contentType = 'video/mp4';
        break;
      case '.webm':
        contentType = 'video/webm';
        break;
      case '.ogg':
        contentType = 'video/ogg';
        break;
      case '.avi':
        contentType = 'video/x-msvideo';
        break;
      case '.mov':
        contentType = 'video/quicktime';
        break;
      default:
        contentType = 'video/mp4';
    }

    if (range) {
      // 处理范围请求（支持视频流播放）
      const parts = range.replace(/bytes=/, "").split("-");
      const start = parseInt(parts[0], 10);
      const end = parts[1] ? parseInt(parts[1], 10) : fileSize - 1;
      const chunksize = (end - start) + 1;

      console.log(`📊 Range: ${start}-${end}/${fileSize} (${chunksize} bytes)`);

      const file = fs.createReadStream(videoPath, { start, end });
      const head = {
        'Content-Range': `bytes ${start}-${end}/${fileSize}`,
        'Accept-Ranges': 'bytes',
        'Content-Length': chunksize,
        'Content-Type': contentType,
        'Cache-Control': 'public, max-age=3600',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Range'
      };

      res.writeHead(206, head);
      file.pipe(res);
    } else {
      // 完整文件响应
      console.log(`📤 Sending complete video file`);

      const head = {
        'Content-Length': fileSize,
        'Content-Type': contentType,
        'Accept-Ranges': 'bytes',
        'Cache-Control': 'public, max-age=3600',
        'Access-Control-Allow-Origin': '*'
      };

      res.writeHead(200, head);
      fs.createReadStream(videoPath).pipe(res);
    }
  } catch (error) {
    console.error(`❌ Error serving video ${filename}:`, error);
    res.status(500).json({
      success: false,
      message: 'Internal server error while serving video'
    });
  }
});

// 图片代理接口
app.get('/api/media/image/:filename', (req, res) => {
  const filename = req.params.filename;
  const imagePath = path.join(IMAGE_DIR, filename);

  console.log(`🖼️ Image request: ${filename}`);
  console.log(`📂 Image path: ${imagePath}`);

  // 检查文件是否存在
  if (!fs.existsSync(imagePath)) {
    console.log(`❌ Image file not found: ${imagePath}`);
    return res.status(404).json({
      success: false,
      message: `Image file not found: ${filename}`
    });
  }

  try {
    // 设置正确的Content-Type
    const ext = path.extname(filename).toLowerCase();
    let contentType = 'image/jpeg';

    switch (ext) {
      case '.jpg':
      case '.jpeg':
        contentType = 'image/jpeg';
        break;
      case '.png':
        contentType = 'image/png';
        break;
      case '.gif':
        contentType = 'image/gif';
        break;
      case '.webp':
        contentType = 'image/webp';
        break;
      case '.svg':
        contentType = 'image/svg+xml';
        break;
      default:
        contentType = 'image/jpeg';
    }

    console.log(`📤 Sending image with content-type: ${contentType}`);

    res.setHeader('Content-Type', contentType);
    res.setHeader('Cache-Control', 'public, max-age=3600');
    res.setHeader('Access-Control-Allow-Origin', '*');

    // 直接发送文件
    fs.createReadStream(imagePath).pipe(res);
  } catch (error) {
    console.error(`❌ Error serving image ${filename}:`, error);
    res.status(500).json({
      success: false,
      message: 'Internal server error while serving image'
    });
  }
});

// 获取媒体文件列表
app.get('/api/media/list', (req, res) => {
  try {
    const videos = fs.existsSync(VIDEO_DIR)
      ? fs.readdirSync(VIDEO_DIR).filter(file =>
          ['.mp4', '.webm', '.ogg', '.avi', '.mov'].includes(path.extname(file).toLowerCase())
        )
      : [];

    const images = fs.existsSync(IMAGE_DIR)
      ? fs.readdirSync(IMAGE_DIR).filter(file =>
          ['.jpg', '.jpeg', '.png', '.gif', '.webp', '.svg'].includes(path.extname(file).toLowerCase())
        )
      : [];

    res.json({
      success: true,
      data: {
        videos: videos.map(filename => ({
          filename,
          url: `http://localhost:${PORT}/api/media/video/${filename}`,
          path: path.join(VIDEO_DIR, filename)
        })),
        images: images.map(filename => ({
          filename,
          url: `http://localhost:${PORT}/api/media/image/${filename}`,
          path: path.join(IMAGE_DIR, filename)
        }))
      },
      message: 'Media files listed successfully'
    });
  } catch (error) {
    console.error('❌ Error listing media files:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to list media files'
    });
  }
});

// 初始化数据库并启动服务器
const startServer = () => {
  try {
    console.log('🔧 Initializing database...');
    initDatabase();
    insertInitialData();
    cleanDuplicateData();
    console.log('✅ Database initialized successfully');

    app.listen(PORT, () => {
      console.log(`📦 Tile proxy server running at http://localhost:${PORT}`);
      console.log(`🎥 Video proxy available at http://localhost:${PORT}/api/media/video/`);
      console.log(`🖼️ Image proxy available at http://localhost:${PORT}/api/media/image/`);
      console.log(`📋 Media list available at http://localhost:${PORT}/api/media/list`);
      console.log(`🗃️ SQLite database: data.db`);
    });
  } catch (error) {
    console.error('❌ Failed to initialize database:', error);
    process.exit(1);
  }
};

startServer();
