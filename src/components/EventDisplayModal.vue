<script setup lang="ts">
import { ref, computed, watch, onMounted, onUnmounted } from 'vue'
import type { EventData } from '@/services/eventApi'

// Props
interface Props {
  visible: boolean
  event: EventData | null
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  'update:visible': [value: boolean]
}>()

// 响应式数据
const showVideo = ref(false)
const imageError = ref(false)
const videoError = ref(false)
const countdown = ref(0)
const countdownTimer = ref<number | null>(null)

// 播放阶段控制
const playbackPhase = ref<'info' | 'video' | 'completed'>('info')
const phaseTimer = ref<number | null>(null)
const videoRef = ref<HTMLVideoElement | null>(null)

// 计算属性
const formattedTime = computed(() => {
  if (!props.event?.eventTime) return ''
  return new Date(props.event.eventTime).toLocaleString('zh-CN')
})



// 方法
const handleImageError = () => {
  imageError.value = true
}

const handleVideoError = () => {
  videoError.value = true
}


// 开始事件播放序列
const startEventPlayback = () => {
  if (!props.event) return

  console.log('开始事件播放序列:', props.event.name)
  console.log('事件时间配置:', {
    preDelay: props.event.preDelay,
    displayDuration: props.event.displayDuration,
    postDelay: props.event.postDelay,
    stayDuration: props.event.stayDuration
  })

  // 重置播放状态
  playbackPhase.value = 'info'
  showVideo.value = false

  // 阶段1: 前置时间 - 显示图文信息
  const preDelayTime = (props.event.preDelay || 0) * 1000

  if (preDelayTime > 0) {
    console.log('阶段1: 前置时间', preDelayTime / 1000, '秒')
    // 显示图文信息
    phaseTimer.value = setTimeout(() => {
      startMainContent()
    }, preDelayTime)
  } else {
    // 没有前置时间，直接开始主要内容
    startMainContent()
  }
}

// 开始主要内容播放
const startMainContent = () => {
  if (!props.event) return

  const displayTime = (props.event.displayDuration || props.event.stayDuration || 5) * 1000
  console.log('阶段2: 主要内容播放时间', displayTime / 1000, '秒')

  if (props.event.videoUrl) {
    // 有视频，切换到视频播放阶段
    playbackPhase.value = 'video'
    showVideo.value = true

    // 等待视频加载并播放
    setTimeout(() => {
      if (videoRef.value) {
        videoRef.value.play().catch(console.error)
      }
    }, 100)

    // 如果视频播放时间不够，用定时器补充
    phaseTimer.value = setTimeout(() => {
      handleContentComplete()
    }, displayTime)
  } else {
    // 没有视频，只显示图文信息
    phaseTimer.value = setTimeout(() => {
      handleContentComplete()
    }, displayTime)
  }
}

// 内容播放完成处理
const handleContentComplete = () => {
  if (!props.event) return

  const postDelayTime = (props.event.postDelay || 0) * 1000

  if (postDelayTime > 0) {
    console.log('阶段3: 后置时间', postDelayTime / 1000, '秒')
    playbackPhase.value = 'completed'

    // 后置延迟
    phaseTimer.value = setTimeout(() => {
      console.log('事件播放序列完成')
      // 不自动关闭，由PlayView控制
    }, postDelayTime)
  } else {
    console.log('事件播放序列完成')
    playbackPhase.value = 'completed'
  }
}

// 视频播放结束处理
const handleVideoEnded = () => {
  console.log('视频播放结束')
  // 视频结束时不立即处理，让定时器控制整体流程
  // 这样可以确保displayDuration的时间控制
}

// 清理定时器
const clearPhaseTimer = () => {
  if (phaseTimer.value) {
    clearTimeout(phaseTimer.value)
    phaseTimer.value = null
  }
}

// 开始倒计时
// const startCountdown = (seconds: number) => {
//   countdown.value = seconds
//   countdownTimer.value = setInterval(() => {
//     countdown.value--
//     if (countdown.value <= 0) {
//       stopCountdown()
//       closeModal()
//     }
//   }, 1000)
// }

// 停止倒计时
const stopCountdown = () => {
  if (countdownTimer.value) {
    clearInterval(countdownTimer.value)
    countdownTimer.value = null
  }
  countdown.value = 0
}

// 关闭弹窗
const closeModal = () => {
  stopCountdown()
  clearPhaseTimer()

  // 停止视频播放
  if (videoRef.value) {
    videoRef.value.pause()
    videoRef.value.currentTime = 0
  }

  // 重置状态
  playbackPhase.value = 'info'
  showVideo.value = false

  emit('update:visible', false)
}

// 阻止事件冒泡
const stopPropagation = (event: Event) => {
  event.stopPropagation()
}

// 监听visible和event变化
watch(() => [props.visible, props.event], ([newVisible, newEvent]) => {

  if (newVisible && newEvent) {
    // 重置状态并开始播放
    showVideo.value = false
    imageError.value = false
    videoError.value = false
    playbackPhase.value = 'info'

    // 开始事件播放序列
    startEventPlayback()
  } else if (!newVisible) {
    console.log('EventDisplayModal: 隐藏事件显示')
    // 隐藏时清理状态
    clearPhaseTimer()
    stopCountdown()
  }
}, { immediate: true })

// 组件挂载时重置状态并开始播放
onMounted(() => {
  showVideo.value = false
  imageError.value = false
  videoError.value = false
  playbackPhase.value = 'info'

  // 开始事件播放序列
  if (props.visible && props.event) {
    startEventPlayback()
  }
})

// 组件卸载时清理定时器
onUnmounted(() => {
  stopCountdown()
  clearPhaseTimer()
})
</script>

<template>
  <!-- 事件详情弹窗 - 全屏显示 -->
  <Transition name="fade" appear>
    <div v-if="visible && event" class="event-display-overlay fullscreen" @click="closeModal">
      <!-- 图文信息阶段 -->
      <Transition name="slide-up" appear>
        <div v-if="visible && event && playbackPhase === 'info'" class="event-info-container" @click="stopPropagation">
          <!-- 关闭按钮 -->
          <button class="close-btn" @click="closeModal">
            <span class="close-icon">✕</span>
          </button>

          <!-- 阶段指示器 -->
          <div class="phase-indicator">
            <div class="phase-text">
              <span v-if="playbackPhase === 'info'" class="phase-label">📖 事件信息</span>
              <span v-else-if="playbackPhase === 'video'" class="phase-label">🎥 视频播放</span>
              <span v-else class="phase-label">✅ 播放完成</span>
            </div>
          </div>

          <!-- 事件头部 -->
          <div class="event-header fullscreen-header">
            <div class="event-meta">
              <h1 class="event-title">{{ event.name }}</h1>
              <div class="event-info">
                <span class="event-location">
                  <span class="info-icon">📍</span>
                  {{ event.location?.name || '未知位置' }}
                </span>
                <span class="event-time">
                  <span class="info-icon">⏰</span>
                  {{ formattedTime }}
                </span>
              </div>
            </div>
          </div>

          <!-- 事件内容 - 图文信息阶段 -->
          <div class="event-content fullscreen-content">
            <!-- 主要图片显示 -->
            <div v-if="event.imageUrl && !imageError" class="main-image-container">
              <img :src="event.imageUrl" :alt="event.name" class="main-event-image" @error="handleImageError" />
            </div>

            <!-- 文字内容区域 -->
            <div class="text-content-area">
              <!-- 描述 -->
              <div v-if="event.description" class="event-description">
                <p>{{ event.description }}</p>
              </div>

              <!-- 详细内容 -->
              <div v-if="event.content" class="event-detail">
                <div class="detail-content">
                  <p>{{ event.content }}</p>
                </div>
              </div>

              <!-- 无内容提示 -->
              <div v-if="!event.description && !event.content" class="no-content">
                <div class="no-content-icon">📄</div>
                <p class="no-content-text">暂无详细内容</p>
              </div>
            </div>

            <!-- 图片加载错误 -->
            <div v-if="imageError" class="media-error">
              <div class="error-icon">❌</div>
              <p class="error-text">图片加载失败</p>
            </div>
          </div>

        </div>
      </Transition>

      <!-- 视频播放阶段 -->
      <Transition name="fade" appear>
        <div v-if="visible && event && playbackPhase === 'video'" class="video-fullscreen-container"
          @click="stopPropagation">
          <!-- 关闭按钮 -->
          <button class="close-btn video-close" @click="closeModal">
            <span class="close-icon">✕</span>
          </button>

          <!-- 视频播放器 -->
          <div class="fullscreen-video-wrapper">
            <video ref="videoRef" v-if="event.videoUrl && !videoError" :src="event.videoUrl" class="fullscreen-video"
              controls @error="handleVideoError" @ended="handleVideoEnded">
              您的浏览器不支持视频播放。
            </video>

            <!-- 视频加载错误 -->
            <div v-if="videoError" class="video-error">
              <div class="error-icon">❌</div>
              <p class="error-text">视频加载失败</p>
              <button class="retry-btn" @click="videoError = false">重试</button>
            </div>
          </div>

          <!-- 视频信息覆盖层 -->
          <div class="video-info-overlay">
            <h2 class="video-title">{{ event.name }}</h2>
            <p class="video-location">📍 {{ event.location?.name || '未知位置' }}</p>
          </div>
        </div>
      </Transition>
    </div>
  </Transition>
</template>

<style scoped>
/* 事件显示弹窗样式 - 全屏 */
.event-display-overlay {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 640px;
  height: 360px;
  background: rgba(0, 0, 0, 0.95);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 25000;
  backdrop-filter: blur(10px);
}

.event-display-overlay.fullscreen {
  background: rgba(0, 0, 0, 1);
}

/* 图文信息容器 - 全屏 */
.event-info-container {
  background: white;
  border-radius: 0;
  width: 100vw;
  height: 100vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  position: relative;
}

/* 视频全屏容器 */
.video-fullscreen-container {
  width: 100vw;
  height: 100vh;
  background: black;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;


}

/* 关闭按钮 */
.close-btn {
  display: none;
  position: absolute;
  top: 30px;
  right: 30px;
  background: rgba(0, 0, 0, 0.3);
  border: none;
  border-radius: 50%;
  width: 50px;
  height: 50px;
  /* display: flex; */
  align-items: center;
  justify-content: center;
  cursor: pointer;
  z-index: 10;
  transition: all 0.2s ease;
  color: white;
  font-size: 18px;
}

.close-btn:hover {
  background: rgba(0, 0, 0, 0.5);
  transform: scale(1.1);
}

.close-btn.video-close {
  background: rgba(0, 0, 0, 0.7);
  color: white;
}

.close-btn.video-close:hover {
  background: rgba(0, 0, 0, 0.9);
}

.close-icon {
  color: white;
  font-size: 18px;
  font-weight: bold;
}

/* 阶段指示器 */
.phase-indicator {
  position: absolute;
  top: 30px;
  left: 30px;
  z-index: 10;
}

.phase-text {
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 14px;
  font-weight: 500;
}

.phase-label {
  display: flex;
  align-items: center;
  gap: 6px;
}

/* 倒计时显示 */
.countdown-display {
  position: absolute;
  top: 20px;
  left: 20px;
  display: flex;
  align-items: center;
  gap: 12px;
  background: rgba(255, 255, 255, 0.95);
  padding: 8px 16px;
  border-radius: 20px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  z-index: 10;
}

.countdown-circle {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: linear-gradient(135deg, #ff6b6b, #ee5a24);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: bold;
  font-size: 14px;
}

.countdown-text {
  color: #333;
  font-size: 14px;
  font-weight: 500;
}

/* 事件头部 */
.event-header {
  padding: 24px 24px 0;
  border-bottom: 1px solid #f0f0f0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 20px;
}

.event-meta {
  flex: 1;
}

.event-title {
  margin: 0 0 12px 0;
  font-size: 24px;
  font-weight: 700;
  line-height: 1.3;
  color: white;
}

.fullscreen-header .event-title {
  font-size: 48px;
  margin: 0 0 20px 0;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.event-info {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  margin-bottom: 16px;
}

.event-location,
.event-time {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 14px;
  color: rgba(255, 255, 255, 0.9);
}

.info-icon {
  font-size: 16px;
}

/* 媒体控制 */
.media-controls {
  display: flex;
  gap: 8px;
  margin-bottom: 16px;
}

.media-btn {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 12px;
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 8px;
  color: white;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.media-btn:hover {
  background: rgba(255, 255, 255, 0.3);
}

.media-btn.active {
  background: rgba(255, 255, 255, 0.9);
  color: #333;
}

.btn-icon {
  font-size: 14px;
}

/* 事件内容 */
.event-content {
  padding: 24px;
  overflow-y: auto;
  flex: 1;
}

.event-content.fullscreen-content {
  padding: 40px 60px;
  display: flex;
  gap: 40px;
  align-items: center;
  justify-content: center;
  min-height: calc(100vh - 200px);
}

.event-description {
  margin-bottom: 20px;
}

.event-description p {
  margin: 0;
  font-size: 16px;
  line-height: 1.6;
  color: #555;
}

.media-content {
  margin-bottom: 20px;
}

.image-container,
.video-container {
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  height: 300px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f8f9fa;
}

.event-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  display: block;
}

/* 全屏主图片容器 */
.main-image-container {
  flex: 1;
  max-width: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.main-event-image {
  width: 100%;
  max-height: 70vh;
  object-fit: contain;
  border-radius: 12px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
}

/* 文字内容区域 */
.text-content-area {
  flex: 1;
  max-width: 50%;
  padding-left: 40px;
}

.text-content-area .event-description {
  margin-bottom: 30px;
}

.text-content-area .event-description p {
  font-size: 20px;
  line-height: 1.6;
  color: #333;
  margin: 0;
}

.text-content-area .event-detail {
  margin-bottom: 0;
}

.text-content-area .detail-content p {
  font-size: 16px;
  line-height: 1.7;
  color: #666;
  margin: 0;
}

.event-video {
  width: 100%;
  height: 100%;
  object-fit: contain;
  display: block;
}

.media-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  background: #f8f9fa;
  border-radius: 12px;
  border: 2px dashed #ddd;
}

.error-icon {
  font-size: 48px;
  margin-bottom: 12px;
}

.error-text {
  color: #666;
  font-size: 16px;
  margin: 0;
}

.event-detail {
  margin-bottom: 20px;
}

.detail-title {
  margin: 0 0 12px 0;
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.detail-content p {
  margin: 0;
  font-size: 15px;
  line-height: 1.7;
  color: #555;
  white-space: pre-wrap;
}

.no-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: #999;
}

.no-content-icon {
  font-size: 48px;
  margin-bottom: 12px;
}

.no-content-text {
  font-size: 16px;
  margin: 0;
}

/* 底部操作 */
.event-footer {
  padding: 20px 24px;
  border-top: 1px solid #f0f0f0;
  background: #fafafa;
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 16px;
}

.event-status {
  flex: 1;
}

.status-badge {
  display: inline-flex;
  align-items: center;
  padding: 6px 12px;
  border-radius: 16px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.status-badge.active {
  background: linear-gradient(135deg, #4facfe, #00f2fe);
  color: white;
}

.status-badge.inactive {
  background: #e9ecef;
  color: #6c757d;
}

.action-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 20px;
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
}

.action-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

/* Vue过渡动画 */
/* 背景淡入淡出 */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

/* 内容滑入滑出 */
.slide-up-enter-active,
.slide-up-leave-active {
  transition: all 0.3s ease;
}

.slide-up-enter-from {
  transform: translateY(50px);
  opacity: 0;
}

.slide-up-leave-to {
  transform: translateY(-30px);
  opacity: 0;
}

/* 全屏视频播放样式 */
.fullscreen-video-wrapper {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.fullscreen-video {
  width: 100%;
  height: 100%;
  max-width: 100vw;
  max-height: 100vh;
  object-fit: contain;
  background: black;
}

.video-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: white;
  text-align: center;
}

.video-error .error-icon {
  font-size: 64px;
  margin-bottom: 20px;
}

.video-error .error-text {
  font-size: 18px;
  margin-bottom: 20px;
  color: white;
}

.retry-btn {
  padding: 10px 20px;
  background: #4CAF50;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  transition: background 0.2s ease;
}

.retry-btn:hover {
  background: #45a049;
}

/* 视频信息覆盖层 */
.video-info-overlay {
  position: absolute;
  bottom: 30px;
  left: 30px;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 15px 20px;
  border-radius: 8px;
  backdrop-filter: blur(10px);
}

.video-title {
  margin: 0 0 5px 0;
  font-size: 18px;
  font-weight: 600;
}

.video-location {
  margin: 0;
  font-size: 14px;
  opacity: 0.9;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .event-info-container {
    width: 100vw;
    height: 100vh;
  }

  .event-header.fullscreen-header {
    padding: 60px 30px 30px;
  }

  .fullscreen-header .event-title {
    font-size: 32px;
  }

  .event-content.fullscreen-content {
    padding: 20px 30px;
    flex-direction: column;
    gap: 30px;
    min-height: calc(100vh - 150px);
  }

  .main-image-container {
    max-width: 100%;
    order: 1;
  }

  .text-content-area {
    max-width: 100%;
    padding-left: 0;
    order: 2;
  }

  .text-content-area .event-description p {
    font-size: 16px;
  }

  .close-btn {
    top: 20px;
    right: 20px;
    width: 40px;
    height: 40px;
    font-size: 16px;
  }

  .phase-indicator {
    top: 20px;
    left: 20px;
  }

  .phase-text {
    padding: 6px 12px;
    font-size: 12px;
  }

  .video-info-overlay {
    bottom: 20px;
    left: 20px;
    padding: 10px 15px;
  }

  .video-title {
    font-size: 16px;
  }

  .video-location {
    font-size: 12px;
  }
}
</style>
