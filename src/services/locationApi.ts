// 位置API服务
export interface LocationData {
  id?: number
  name?: string
  longitude: number
  latitude: number
  height: number
  country?: string
  city?: string
  stayDuration?: number // 位置停留时间（秒）
  isDefault?: boolean
  createdAt?: string
  updatedAt?: string
}

export interface ApiResponse<T> {
  success: boolean
  data: T
  message: string
}

const API_BASE_URL = 'http://localhost:8082/api'

/**
 * 获取默认位置
 */
export async function getDefaultLocation(): Promise<LocationData> {
  try {
    const response = await fetch(`${API_BASE_URL}/default-location`)

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }

    const result: ApiResponse<LocationData> = await response.json()

    if (!result.success) {
      throw new Error(result.message || 'Failed to get default location')
    }

    return result.data
  } catch (error) {
    console.error('Error fetching default location:', error)
    // 返回默认值作为后备
    return {
      longitude: 116.3974, // 北京经度
      latitude: 39.9093, // 北京纬度
      height: 10000000, // 高度（米）
    }
  }
}

/**
 * 更新默认位置
 */
export async function updateDefaultLocation(location: LocationData): Promise<LocationData> {
  try {
    const response = await fetch(`${API_BASE_URL}/default-location`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(location),
    })

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }

    const result: ApiResponse<LocationData> = await response.json()

    if (!result.success) {
      throw new Error(result.message || 'Failed to update default location')
    }

    return result.data
  } catch (error) {
    console.error('Error updating default location:', error)
    throw error
  }
}

/**
 * 获取所有位置
 */
export async function getAllLocations(): Promise<LocationData[]> {
  try {
    const response = await fetch(`${API_BASE_URL}/locations`)

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }

    const result: ApiResponse<LocationData[]> = await response.json()

    if (!result.success) {
      throw new Error(result.message || 'Failed to get locations')
    }

    return result.data
  } catch (error) {
    console.error('Error fetching locations:', error)
    throw error
  }
}

/**
 * 根据ID获取位置
 */
export async function getLocationById(id: number): Promise<LocationData> {
  try {
    const response = await fetch(`${API_BASE_URL}/locations/${id}`)

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }

    const result: ApiResponse<LocationData> = await response.json()

    if (!result.success) {
      throw new Error(result.message || 'Failed to get location')
    }

    return result.data
  } catch (error) {
    console.error('Error fetching location:', error)
    throw error
  }
}

/**
 * 创建新位置
 */
export async function createLocation(
  location: Omit<LocationData, 'id' | 'createdAt' | 'updatedAt'>,
): Promise<LocationData> {
  try {
    const response = await fetch(`${API_BASE_URL}/locations`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(location),
    })

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }

    const result: ApiResponse<LocationData> = await response.json()

    if (!result.success) {
      throw new Error(result.message || 'Failed to create location')
    }

    return result.data
  } catch (error) {
    console.error('Error creating location:', error)
    throw error
  }
}

/**
 * 更新位置
 */
export async function updateLocation(
  id: number,
  location: Partial<Omit<LocationData, 'id' | 'createdAt' | 'updatedAt'>>,
): Promise<LocationData> {
  try {
    const response = await fetch(`${API_BASE_URL}/locations/${id}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(location),
    })

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }

    const result: ApiResponse<LocationData> = await response.json()

    if (!result.success) {
      throw new Error(result.message || 'Failed to update location')
    }

    return result.data
  } catch (error) {
    console.error('Error updating location:', error)
    throw error
  }
}

/**
 * 删除位置
 */
export async function deleteLocation(id: number): Promise<LocationData> {
  try {
    const response = await fetch(`${API_BASE_URL}/locations/${id}`, {
      method: 'DELETE',
    })

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }

    const result: ApiResponse<LocationData> = await response.json()

    if (!result.success) {
      throw new Error(result.message || 'Failed to delete location')
    }

    return result.data
  } catch (error) {
    console.error('Error deleting location:', error)
    throw error
  }
}

/**
 * 设置默认位置
 */
export async function setDefaultLocation(id: number): Promise<LocationData> {
  try {
    const response = await fetch(`${API_BASE_URL}/locations/${id}/set-default`, {
      method: 'PATCH',
    })

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }

    const result: ApiResponse<LocationData> = await response.json()

    if (!result.success) {
      throw new Error(result.message || 'Failed to set default location')
    }

    return result.data
  } catch (error) {
    console.error('Error setting default location:', error)
    throw error
  }
}
