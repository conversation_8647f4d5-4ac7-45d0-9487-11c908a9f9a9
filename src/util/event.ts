// 创建事件图标Canvas
// const createEventIconCanvasBaK = (iconText: string, color: string = '#FF5722') => {
//   const canvas = document.createElement('canvas')
//   const size = 24
//   // const dpr = window.devicePixelRatio || 1
//   canvas.width = size
//   canvas.height = size
//   canvas.style.width = `${size}px`
//   canvas.style.height = `${size}px`

//   const ctx = canvas.getContext('2d')!
//   // ctx.scale(dpr, dpr)

//   // 绘制圆形背景
//   ctx.fillStyle = color
//   ctx.beginPath()
//   ctx.arc(size / 2, size / 2, size / 2 - 2, 0, 2 * Math.PI)
//   ctx.fill()

//   // 绘制白色边框
//   ctx.strokeStyle = '#FFFFFF'
//   ctx.lineWidth = 1
//   ctx.stroke()

//   // 绘制emoji图标
//   // ctx.font = `${size}px` // 保持字体大小与图标尺寸一致
//   // ctx.textAlign = 'center'
//   // ctx.textBaseline = 'middle'
//   // ctx.fillStyle = '#FFFFFF'
//   // ctx.fillText(iconText, size / 2, size / 2)

//   return canvas
// }

// 创建事件图标Canvas
const createEventIconCanvas = (iconText: string, color: string = '#FF5722') => {
  const canvas = document.createElement('canvas')
  const size = 32
  canvas.width = size
  canvas.height = size

  const ctx = canvas.getContext('2d')
  if (!ctx) return canvas

  // 绘制圆形背景
  ctx.fillStyle = color + '66'
  ctx.beginPath()
  ctx.arc(size / 2, size / 2, size / 2 - 2, 0, 2 * Math.PI)
  ctx.fill()

  // 绘制白色边框
  ctx.strokeStyle = 'white'
  ctx.lineWidth = 1
  ctx.stroke()

  // 绘制图标文字
  ctx.fillStyle = 'white'
  ctx.font = 'bold 24px'
  ctx.textAlign = 'center'
  ctx.textBaseline = 'middle'
  ctx.fillText(iconText, size / 2, size / 2)

  return canvas
}

export { createEventIconCanvas }
