<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import { useRoute } from 'vue-router'
import { getAnimationById, type AnimationData, type AnimationPath } from '@/services/animationApi'
import { getEventById, getAllEvents, type EventData } from '@/services/eventApi'
import EventDisplayModal from '@/components/EventDisplayModal.vue'

import {
  Entity,
  Viewer,
  Cartesian3,
  HeightReference,
  VerticalOrigin,
  HorizontalOrigin,
  NearFarScalar,
  Color,
  LabelStyle,
  Cartesian2
} from 'cesium'
import { createEventIconCanvas } from '@/util/event'
declare global {
  interface Window {
    initCesium: (id: string) => Viewer
  }
}

// 路由参数
const route = useRoute()
const animationId = parseInt(route.params.id as string)

// 响应式数据
const cesiumContainer = ref<HTMLElement>()
const loading = ref(true)
const error = ref<string>('')
const animation = ref<AnimationData | null>(null)
const viewer = ref<Viewer | null>(null)
const currentEvent = ref<EventData | null>(null)
const showEventDisplay = ref(false)
const playbackStatus = ref<'playing' | 'paused' | 'stopped'>('stopped')
const currentLocationIndex = ref(0)
const totalLocations = ref(0)

// 控制面板显示状态
const showControlPanel = ref(true)
const controlPanelHideTimeout = ref<number | null>(null)

// 播放控制
const isPlaying = ref(false)
const currentAnimationTimeout = ref<number | null>(null)

// 事件图标相关
const showEventIcons = ref(true)
const eventEntities = ref<Entity[]>([])
const allEvents = ref<EventData[]>([])

// 初始化Cesium
const initCesium = async () => {
  if (!cesiumContainer.value) {
    console.error('Cesium container not found')
    return
  }

  try {
    console.log('Initializing Cesium...')



    // 为容器设置一个唯一ID
    const containerId = 'play-cesium-container'
    cesiumContainer.value.id = containerId

    console.log('Calling initCesium with container ID:', containerId)
    console.log('Container element:', cesiumContainer.value)

    // 使用全局初始化函数
    try {
      viewer.value = window.initCesium(containerId)
    } catch (initError) {
      console.error('Error during initCesium call:', initError)
      throw new Error('initCesium failed: ' + (initError as Error).message)
    }

    if (!viewer.value) {
      throw new Error('initCesium returned null or undefined')
    }

    console.log('Cesium viewer initialized successfully:', viewer.value)
    console.log('Viewer type:', typeof viewer.value)
    console.log('Viewer camera:', viewer.value.camera)
  } catch (err) {
    console.error('Failed to initialize Cesium:', err)
    error.value = '初始化地球视图失败: ' + (err as Error).message
  }
}

// 加载动画数据
const loadAnimation = async () => {
  try {
    loading.value = true
    animation.value = await getAnimationById(animationId)
    totalLocations.value = animation.value.paths.length

    if (animation.value.paths.length > 0) {
      // 设置默认位置为动画的第一个位置
      const firstLocation = animation.value.paths[0].location
      if (firstLocation && viewer.value && viewer.value.camera) {

        viewer.value.camera.flyTo({
          destination: Cartesian3.fromDegrees(
            firstLocation.longitude,
            firstLocation.latitude,
            firstLocation.height
          ),
          duration: 2.0 // 2秒飞行到初始位置
        })

      }
    }

    console.log('Animation loaded:', animation.value)
  } catch (err) {
    console.error('Failed to load animation:', err)
    error.value = '加载动画失败'
  } finally {
    loading.value = false
  }
}

// 隐藏控制面板
const hideControlPanel = () => {
  showControlPanel.value = false
}

// 显示控制面板
const showControlPanelTemporarily = () => {
  showControlPanel.value = true

  // 清除之前的定时器
  if (controlPanelHideTimeout.value) {
    clearTimeout(controlPanelHideTimeout.value)
  }

  // 如果正在播放，3秒后自动隐藏
  if (playbackStatus.value === 'playing') {
    controlPanelHideTimeout.value = setTimeout(() => {
      hideControlPanel()
    }, 3000)
  }
}

// 播放动画
const playAnimation = async () => {
  if (!animation.value || !viewer.value || isPlaying.value) return

  isPlaying.value = true
  playbackStatus.value = 'playing'
  currentLocationIndex.value = 0

  // 播放开始时隐藏控制面板
  hideControlPanel()


  for (let i = 0; i < animation.value.paths.length; i++) {
    if (!isPlaying.value) break // 检查是否被停止

    const path = animation.value.paths[i]
    const location = path.location
    if (!location) continue

    currentLocationIndex.value = i

    // 飞行到位置
    await new Promise<void>((resolve) => {
      if (viewer.value && viewer.value.camera && viewer.value.camera.flyTo) {
        viewer.value.camera.flyTo({
          destination: Cartesian3.fromDegrees(
            location.longitude,
            location.latitude,
            location.height
          ),
          duration: path.transitionDuration,
          complete: resolve
        })
      } else {
        console.error('Camera flyTo not available')
        resolve()
      }
    })

    if (!isPlaying.value) break

    // 处理位置事件
    await handleLocationEvents(path)

    if (!isPlaying.value) break
  }

  // 动画播放完成
  if (isPlaying.value) {
    if (animation.value.isLoop) {
      // 循环播放
      playAnimation()
    } else {
      stopAnimation()
      // 播放完成后显示控制面板
      showControlPanel.value = true
    }
  }
}

// 处理位置事件
const handleLocationEvents = async (path: AnimationPath) => {
  const location = path.location
  if (!location) return

  // 如果指定了事件，播放该事件
  if (path.selectedEventId) {
    console.log('查找事件ID:', path.selectedEventId, '位置:', location.name)

    try {
      // 直接通过事件ID获取事件，而不是通过locationId
      const selectedEvent = await getEventById(path.selectedEventId)

      if (selectedEvent) {
        console.log('找到事件:', selectedEvent.name)
        console.log('事件配置:', {
          preDelay: selectedEvent.preDelay,
          displayDuration: selectedEvent.displayDuration,
          stayDuration: selectedEvent.stayDuration,
          postDelay: selectedEvent.postDelay
        })

        // 显示事件（EventDisplayModal会自动处理前置时间、播放时长、后置时间）
        currentEvent.value = selectedEvent
        showEventDisplay.value = true

        // 等待事件完整播放完成
        // 总时间 = 前置时间 + 停留时间 + 后置时间
        const totalEventTime = (selectedEvent.preDelay + selectedEvent.stayDuration + selectedEvent.postDelay) * 1000
        console.log('事件总播放时间:', totalEventTime / 1000, '秒')

        await delay(totalEventTime)
        if (!isPlaying.value) return

        // 隐藏事件（先隐藏，等动画完成后清除事件数据）
        showEventDisplay.value = false

        // 等待淡出动画完成
        await delay(300)
        currentEvent.value = null

        console.log('事件播放完成:', selectedEvent.name)
      } else {
        console.log('未找到事件ID:', path.selectedEventId)
        // 没有找到事件，使用位置的停留时间
        await delay(location.stayDuration * 1000)
      }
    } catch (error) {
      console.error('获取事件失败:', error)
      // 出错时使用位置的停留时间
      await delay(location.stayDuration * 1000)
    }
  } else {
    // 没有指定事件，使用位置的停留时间
    console.log('位置停留时间:', location.stayDuration, '秒')
    await delay(location.stayDuration * 1000)
  }
}

// 延迟函数
const delay = (ms: number): Promise<void> => {
  return new Promise(resolve => {
    currentAnimationTimeout.value = setTimeout(resolve, ms)
  })
}

// 加载所有事件
const loadAllEvents = async () => {
  try {
    console.log('PlayView: 加载所有事件...')
    const events = await getAllEvents()
    allEvents.value = events.filter(event => event.isActive) // 只显示活跃事件
    console.log('PlayView: 加载了', allEvents.value.length, '个活跃事件')
  } catch (error) {
    console.error('PlayView: 加载事件失败:', error)
  }
}


// 显示事件图标
const showEventIconsOnMap = () => {


  // 清除现有的事件实体
  clearEventIcons()

  if (!showEventIcons.value) {
    console.log('PlayView: Event icons are disabled')
    return
  }

  console.log(`PlayView: Showing ${allEvents.value.length} events on map`)


  allEvents.value.forEach((event) => {
    if (!event.location) {
      console.warn(`PlayView: Event ${event.id} has no location data`)
      return
    }

    // 图标贴着地面显示，高度设为0
    const position = Cartesian3.fromDegrees(
      event.location.longitude,
      event.location.latitude,
      0 // 贴地显示
    )

    // 确定图标文字和颜色
    let iconText = '📅'
    let iconColor = '#9C27B0'

    // 优先使用自定义图标
    if (event.customIcon) {
      iconText = event.customIcon
      iconColor = event.iconColor || '#9C27B0'
    } else {
      // 根据媒体类型选择图标
      if (event.videoUrl && event.imageUrl) {
        iconText = '🎬' // 视频+图片
        iconColor = event.iconColor || '#FF5722' // 红色
      } else if (event.videoUrl) {
        iconText = '🎥' // 仅视频
        iconColor = event.iconColor || '#FF9800' // 橙色
      } else if (event.imageUrl) {
        iconText = '🖼️' // 仅图片
        iconColor = event.iconColor || '#2196F3' // 蓝色
      }
    }

    // 创建图标canvas
    const iconCanvas = createEventIconCanvas(iconText, iconColor)

    try {
      const entity = viewer.value!.entities.add({
        position: position,
        billboard: {
          image: iconCanvas,
          // width: 64, // 固定宽度
          // height: 64, // 固定高度
          heightReference: HeightReference.CLAMP_TO_GROUND, // 贴地显示
          verticalOrigin: VerticalOrigin.BOTTOM,
          horizontalOrigin: HorizontalOrigin.CENTER,
          // 移除距离缩放，保持固定大小
          // scaleByDistance: new Cesium.NearFarScalar(1000, 1.0, 10000000, 0.3),
          // translucencyByDistance: new NearFarScalar(1000, 1.0, 10000000, 0.8),
          disableDepthTestDistance: Number.POSITIVE_INFINITY
        },
        label: {
          text: event.name,
          font: '12pt Arial', // 稍微增大字体
          fillColor: Color.WHITE,
          outlineColor: Color.BLACK,
          outlineWidth: 1,
          style: LabelStyle.FILL_AND_OUTLINE,
          verticalOrigin: VerticalOrigin.TOP,
          horizontalOrigin: HorizontalOrigin.CENTER,
          pixelOffset: new Cartesian2(0, 10),
          heightReference: HeightReference.CLAMP_TO_GROUND, // 贴地显示
          // 移除距离缩放，保持固定大小
          // scaleByDistance: new Cesium.NearFarScalar(1000, 1.0, 10000000, 0.3),
          translucencyByDistance: new NearFarScalar(1000, 1.0, 10000000, 0.8),
          disableDepthTestDistance: Number.POSITIVE_INFINITY
        }
      })

      // 添加点击事件
      // entity.eventData = event
      eventEntities.value.push(entity)

      console.log(`PlayView: Created ground-clamped icon for event ${event.id}: ${event.name}`)
    } catch (error) {
      console.error(`PlayView: Failed to create entity for event ${event.id}:`, error)
    }
  })

  console.log(`PlayView: Created ${eventEntities.value.length} event icons`)
}

// 清除事件图标
const clearEventIcons = () => {
  if (!viewer.value) return

  console.log(`PlayView: Clearing ${eventEntities.value.length} event icons`)

  eventEntities.value.forEach(entity => {
    try {
      viewer.value!.entities.remove(entity)
    } catch (error) {
      console.warn('PlayView: Failed to remove entity:', error)
    }
  })

  eventEntities.value = []
  console.log('PlayView: Event icons cleared')
}

// 切换事件图标显示
const toggleEventIcons = async () => {
  showEventIcons.value = !showEventIcons.value
  console.log('PlayView: Toggling event icons:', showEventIcons.value ? 'showing' : 'hiding')

  if (!viewer.value) {
    console.error('PlayView: Cannot toggle icons: Cesium viewer not available')
    return
  }

  if (showEventIcons.value) {
    // 如果事件数据为空，先加载事件
    if (allEvents.value.length === 0) {
      console.log('PlayView: No events loaded, loading events first...')
      await loadAllEvents()
    }
    showEventIconsOnMap()
  } else {
    clearEventIcons()
  }

}

// 停止动画
const stopAnimation = () => {
  isPlaying.value = false
  playbackStatus.value = 'stopped'
  currentLocationIndex.value = 0

  if (currentAnimationTimeout.value) {
    clearTimeout(currentAnimationTimeout.value)
    currentAnimationTimeout.value = null
  }

  // 清除控制面板隐藏定时器
  if (controlPanelHideTimeout.value) {
    clearTimeout(controlPanelHideTimeout.value)
    controlPanelHideTimeout.value = null
  }

  // 隐藏事件显示
  showEventDisplay.value = false
  currentEvent.value = null

  // 停止时显示控制面板
  showControlPanel.value = true
}

// 暂停动画
const pauseAnimation = () => {
  isPlaying.value = false
  playbackStatus.value = 'paused'

  if (currentAnimationTimeout.value) {
    clearTimeout(currentAnimationTimeout.value)
    currentAnimationTimeout.value = null
  }

  // 清除控制面板隐藏定时器
  if (controlPanelHideTimeout.value) {
    clearTimeout(controlPanelHideTimeout.value)
    controlPanelHideTimeout.value = null
  }

  // 暂停时显示控制面板
  showControlPanel.value = true
}

// 恢复动画
const resumeAnimation = () => {
  if (playbackStatus.value === 'paused') {
    isPlaying.value = true
    playbackStatus.value = 'playing'

    // 恢复播放时隐藏控制面板
    hideControlPanel()

    // 从当前位置继续播放
    continueFromCurrentLocation()
  }
}

// 从当前位置继续播放
const continueFromCurrentLocation = async () => {
  if (!animation.value || !viewer.value) return

  for (let i = currentLocationIndex.value; i < animation.value.paths.length; i++) {
    if (!isPlaying.value) break

    const path = animation.value.paths[i]
    const location = path.location
    if (!location) continue

    currentLocationIndex.value = i

    // 如果不是当前位置，需要飞行过去
    if (i > currentLocationIndex.value) {

      await new Promise<void>((resolve) => {
        if (viewer.value && viewer.value.camera && viewer.value.camera.flyTo) {
          viewer.value.camera.flyTo({
            destination: Cartesian3.fromDegrees(
              location.longitude,
              location.latitude,
              location.height
            ),
            duration: path.transitionDuration,
            complete: resolve
          })
        } else {
          console.error('Camera flyTo not available')
          resolve()
        }
      })
    }

    if (!isPlaying.value) break

    // 处理位置事件
    await handleLocationEvents(path)

    if (!isPlaying.value) break
  }

  // 播放完成处理
  if (isPlaying.value) {
    if (animation.value.isLoop) {
      playAnimation()
    } else {
      stopAnimation()
      // 播放完成后显示控制面板
      showControlPanel.value = true
    }
  }
}

// 关闭事件显示
const closeEventDisplay = () => {
  showEventDisplay.value = false
  currentEvent.value = null
}

// 组件挂载
onMounted(async () => {
  // 等待DOM完全渲染
  await new Promise(resolve => setTimeout(resolve, 100))
  await initCesium()
  await loadAnimation()
  await loadAllEvents()

  // 默认显示事件图标
  if (showEventIcons.value) {
    showEventIconsOnMap()
  }
})

// 组件卸载
onUnmounted(() => {
  stopAnimation()

  // 清除控制面板隐藏定时器
  if (controlPanelHideTimeout.value) {
    clearTimeout(controlPanelHideTimeout.value)
    controlPanelHideTimeout.value = null
  }

  // 清理事件图标
  clearEventIcons()

  // 注意：GeoGlobe SDK的viewer可能不需要手动销毁
  // 或者销毁方法名称不同，暂时注释掉
  // if (viewer.value && viewer.value.destroy) {
  //   viewer.value.destroy()
  // }
})
</script>

<template>
  <div class="play-view">
    <!-- 加载状态 -->
    <div v-if="loading" class="loading-overlay">
      <div class="loading-spinner"></div>
      <div class="loading-text">加载动画中...</div>
    </div>

    <!-- 错误状态 -->
    <div v-if="error" class="error-overlay">
      <div class="error-icon">❌</div>
      <div class="error-text">{{ error }}</div>
      <button class="retry-btn" @click="loadAnimation">重试</button>
    </div>

    <!-- Cesium容器 -->
    <div ref="cesiumContainer" class="cesium-container" @mousemove="showControlPanelTemporarily"></div>

    <!-- 播放控制面板 -->
    <Transition name="control-panel">
      <div v-if="animation && showControlPanel" class="control-panel">
        <div class="animation-info">
          <h3>{{ animation.name }}</h3>
          <p>{{ animation.description }}</p>
          <div class="progress-info">
            位置 {{ currentLocationIndex + 1 }} / {{ totalLocations }}
          </div>
        </div>

        <div class="control-buttons">
          <button v-if="playbackStatus === 'stopped'" class="control-btn play" @click="playAnimation"
            :disabled="loading">
            ▶️ 播放
          </button>

          <button v-if="playbackStatus === 'playing'" class="control-btn pause" @click="pauseAnimation">
            ⏸️ 暂停
          </button>

          <button v-if="playbackStatus === 'paused'" class="control-btn resume" @click="resumeAnimation">
            ▶️ 继续
          </button>

          <button v-if="playbackStatus !== 'stopped'" class="control-btn stop" @click="stopAnimation">
            ⏹️ 停止
          </button>

          <!-- 事件图标控制 -->
          <button class="control-btn icon-toggle" @click="toggleEventIcons" :class="{ active: showEventIcons }">
            {{ showEventIcons ? '🎯' : '👁️' }} {{ showEventIcons ? '隐藏图标' : '显示图标' }}
          </button>
        </div>
      </div>
    </Transition>

    <!-- 事件显示 -->
    <EventDisplayModal v-if="currentEvent" :visible="showEventDisplay" :event="currentEvent"
      @update:visible="closeEventDisplay" />
  </div>
</template>

<style scoped>
.play-view {
  position: relative;
  width: 100vw;
  height: 100vh;
  overflow: hidden;
}

.cesium-container {
  width: 100%;
  height: 100%;
}

.loading-overlay,
.error-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  color: white;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid rgba(255, 255, 255, 0.3);
  border-top: 4px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

.loading-text,
.error-text {
  font-size: 18px;
  font-weight: 500;
}

.error-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

.retry-btn {
  margin-top: 16px;
  padding: 10px 20px;
  background: #007bff;
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  cursor: pointer;
  transition: background 0.2s;
}

.retry-btn:hover {
  background: #0056b3;
}

.control-panel {
  position: absolute;
  bottom: 20px;
  left: 20px;
  right: 20px;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(10px);
  border-radius: 12px;
  padding: 20px;
  color: white;
  display: flex;
  justify-content: space-between;
  align-items: center;
  z-index: 100;
}

.animation-info h3 {
  margin: 0 0 8px 0;
  font-size: 18px;
  font-weight: 600;
}

.animation-info p {
  margin: 0 0 8px 0;
  font-size: 14px;
  opacity: 0.8;
}

.progress-info {
  font-size: 12px;
  opacity: 0.6;
}

.control-buttons {
  display: flex;
  gap: 12px;
}

.control-btn {
  padding: 10px 16px;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  min-width: 80px;
}

.control-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.control-btn.play,
.control-btn.resume {
  background: #28a745;
  color: white;
}

.control-btn.play:hover:not(:disabled),
.control-btn.resume:hover:not(:disabled) {
  background: #218838;
}

.control-btn.pause {
  background: #ffc107;
  color: #212529;
}

.control-btn.pause:hover {
  background: #e0a800;
}

.control-btn.stop {
  background: #dc3545;
  color: white;
}

.control-btn.stop:hover {
  background: #c82333;
}

.control-btn.icon-toggle {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: white;
}

.control-btn.icon-toggle.active {
  background: rgba(76, 175, 80, 0.8);
  border-color: #4CAF50;
  color: white;
}

.control-btn.icon-toggle:hover {
  background: rgba(255, 255, 255, 0.2);
}

.control-btn.icon-toggle.active:hover {
  background: rgba(76, 175, 80, 0.9);
}

/* 控制面板过渡动画 */
.control-panel-enter-active,
.control-panel-leave-active {
  transition: all 0.3s ease;
}

.control-panel-enter-from {
  opacity: 0;
  transform: translateY(100%);
}

.control-panel-leave-to {
  opacity: 0;
  transform: translateY(100%);
}

@media (max-width: 768px) {
  .control-panel {
    flex-direction: column;
    gap: 16px;
    text-align: center;
  }

  .control-buttons {
    justify-content: center;
  }
}
</style>
