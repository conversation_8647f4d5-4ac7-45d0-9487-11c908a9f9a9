<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { highDPIOptimizer } from '@/util/highDPI'
import EarthRotationControl from '@/components/EarthRotationControl.vue'
import LocationManagementModal from '@/components/LocationManagementModal.vue'
import AnimationManagementModal from '@/components/AnimationManagementModal.vue'
import EventManagementModal from '@/components/EventManagementModal.vue'
import EventDisplayModal from '@/components/EventDisplayModal.vue'
import EventSelectionModal from '@/components/EventSelectionModal.vue'
import {
  getDefaultLocation,
  createLocation,
  getAllLocations,
  type LocationData,
} from '@/services/locationApi'
import {
  type EventData,
  getEventsByLocationId,
  getEventById,
  getAllEvents,
} from '@/services/eventApi'

import { Viewer, Entity, Cartesian3, Math as CesiumMath, Cartographic, Color, HeightReference, ScreenSpaceEventType, Cartesian2, LabelStyle, VerticalOrigin, ScreenSpaceEventHandler } from 'cesium'
import { createEventIconCanvas } from '@/util/event'

declare global {
  interface Window {
    initCesium: (id: string) => Viewer
  }
}

const rootEl = ref<HTMLDivElement | null>(null)
const cesiumViewer = ref<Viewer | null>(null)
const locations = ref<LocationData[]>([])
const showLocationModal = ref(false)
const showAnimationModal = ref(false)
const showEventModal = ref(false)
const showEventDisplay = ref(false)

// // 监听showEventDisplay变化
// watch(showEventDisplay, (newVal) => {
//   console.log('EarthView: showEventDisplay 变化为', newVal)
// })

// // 监听currentEvent变化
// watch(currentEvent, (newVal) => {
//   console.log('EarthView: currentEvent 变化为', newVal?.name || null)
// })
const showEventSelection = ref(false)
const currentEvent = ref<EventData | null>(null)
const locationEvents = ref<EventData[]>([])
const currentLocationName = ref('')
const isClickToAddMode = ref(false)
const mousePosition = ref({ longitude: 0, latitude: 0, height: 0, visible: false })
const showEventIcons = ref(true)
const eventEntities = ref<Entity[]>([])
const allEvents = ref<EventData[]>([])

// 打开位置管理弹窗
const openLocationModal = () => {
  showLocationModal.value = true
}

// 打开动画管理弹窗
const openAnimationModal = () => {
  showAnimationModal.value = true
}

// 打开事件管理弹窗
const openEventModal = () => {
  showEventModal.value = true
}

// 处理位置变更事件
const handleLocationChanged = () => {
  // 可以在这里添加刷新地球视图的逻辑
  console.log('Location changed, refreshing view...')
}

// 处理动画变更事件
const handleAnimationChanged = () => {
  // 可以在这里添加刷新动画列表的逻辑
  console.log('Animation changed, refreshing animations...')
}

// 处理事件变更事件
const handleEventChanged = async () => {
  // 刷新事件列表和地图图标
  console.log('Event changed, refreshing events...')
  await loadAllEvents()
  if (cesiumViewer.value) {
    showEventIconsOnMap()
  }
}

// 显示事件详情
const handleShowEvent = (event: EventData) => {
  currentEvent.value = event
  showEventDisplay.value = true
  console.log('Show event:', event)
}

// 显示位置相关事件（动画播放时调用）
const handleShowLocationEvents = async (locationId: number, selectedEventId?: number) => {
  console.log('EarthView: handleShowLocationEvents 被调用', { locationId, selectedEventId })
  try {
    // 获取位置名称
    const location = locations.value?.find((loc) => loc.id === locationId)
    currentLocationName.value = location?.name || '未知位置'

    // 如果指定了特定事件ID，直接通过事件ID获取事件
    if (selectedEventId) {
      console.log('EarthView: 查找事件ID:', selectedEventId, '位置:', currentLocationName.value)

      try {
        // 直接通过事件ID获取事件，而不是通过locationId
        const selectedEvent = await getEventById(selectedEventId)

        if (selectedEvent) {
          console.log('EarthView: 找到事件:', selectedEvent.name)
          console.log('EarthView: 事件配置:', {
            preDelay: selectedEvent.preDelay,
            displayDuration: selectedEvent.displayDuration,
            stayDuration: selectedEvent.stayDuration,
            postDelay: selectedEvent.postDelay,
          })

          // 直接显示事件，让EventDisplayModal内部处理前置时间
          currentEvent.value = selectedEvent
          showEventDisplay.value = true
          console.log('EarthView: Show selected event immediately:', selectedEvent.name)
          return
        } else {
          console.log('EarthView: 未找到事件ID:', selectedEventId)
        }
      } catch (error) {
        console.error('EarthView: 获取事件失败:', error)
      }
    }

    // 否则按原逻辑处理
    const events = await getEventsByLocationId(locationId)
    if (events.length === 0) {
      // 没有事件，直接返回
      console.log('No events found for location:', locationId)
      return
    }

    // 过滤活跃事件
    const activeEvents = events.filter((event) => event.isActive)
    const displayEvents = activeEvents.length > 0 ? activeEvents : events

    if (displayEvents.length === 1) {
      // 只有一个事件（或只有一个活跃事件），直接显示
      const singleEvent = displayEvents[0]
      currentEvent.value = singleEvent
      showEventDisplay.value = true
      console.log('Show single location event immediately:', singleEvent)
    } else {
      // 多个事件，显示选择界面
      locationEvents.value = events // 传递所有事件，让选择组件内部处理活跃状态
      showEventSelection.value = true
      console.log(
        'Show event selection for location:',
        currentLocationName.value,
        'total events:',
        events.length,
        'active events:',
        activeEvents.length,
      )
    }
  } catch (error) {
    console.error('Failed to load location events:', error)
    // 可以显示一个错误通知
    console.log('Error loading events for location:', locationId, error)
  }
}

// 处理事件选择
const handleEventSelected = (event: EventData) => {
  // 直接显示事件，让EventDisplayModal内部处理前置时间
  currentEvent.value = event
  showEventDisplay.value = true
  console.log('Event selected and shown immediately:', event)
}

// 处理跳过事件
const handleSkipEvents = () => {
  console.log('Events skipped for location:', currentLocationName.value)
}

// 加载位置数据
const loadLocations = async () => {
  try {
    locations.value = await getAllLocations()
  } catch (error) {
    console.error('Failed to load locations:', error)
  }
}

// 跳转到指定位置
const handleJumpToLocation = (location: LocationData) => {
  if (!cesiumViewer.value) {
    console.error('Cesium viewer not available')
    return
  }

  console.log('Jumping to location:', location)

  // 使用Cesium的flyTo方法跳转到指定位置
  cesiumViewer.value.camera.flyTo({
    destination: Cartesian3.fromDegrees(
      location.longitude,
      location.latitude,
      location.height,
    ),
    orientation: {
      heading: CesiumMath.toRadians(0),
      pitch: CesiumMath.toRadians(-90),
      roll: CesiumMath.toRadians(0),
    },
    duration: 3.0, // 动画持续时间（秒）
    complete: () => {
      console.log(`Successfully jumped to: ${location.name}`)
    },
  })
}

// 根据相机高度计算合理的默认高度
const calculateDefaultHeight = (viewer: Viewer): number => {

  // 获取当前相机位置
  const cameraPosition = viewer.camera.position
  const cartographic = Cartographic.fromCartesian(cameraPosition)
  const cameraHeight = cartographic.height

  console.log(`Current camera height: ${cameraHeight.toFixed(0)}m`)

  // 根据相机高度设置合理的默认高度
  let defaultHeight: number

  if (cameraHeight > 20000000) {
    // 全球视角：设置为较高的高度
    defaultHeight = 15000000
  } else if (cameraHeight > 5000000) {
    // 大陆视角：设置为中等高度
    defaultHeight = Math.max(3000000, cameraHeight * 0.6)
  } else if (cameraHeight > 1000000) {
    // 国家/省份视角：设置为较低的高度
    defaultHeight = Math.max(500000, cameraHeight * 0.5)
  } else if (cameraHeight > 100000) {
    // 城市视角：设置为城市级别的高度
    defaultHeight = Math.max(50000, cameraHeight * 0.4)
  } else if (cameraHeight > 10000) {
    // 区域视角：设置为区域级别的高度
    defaultHeight = Math.max(5000, cameraHeight * 0.3)
  } else {
    // 近距离视角：设置为较低的高度
    defaultHeight = Math.max(1000, cameraHeight * 0.2)
  }

  console.log(`Calculated default height: ${defaultHeight.toFixed(0)}m`)
  return Math.round(defaultHeight)
}

// 设置地球点击事件处理
const setupEarthClickHandler = (viewer: Viewer) => {

  const handler = new ScreenSpaceEventHandler(viewer.scene.canvas)

  handler.setInputAction(
    (event: ScreenSpaceEventHandler.PositionedEvent) => {
      // 只有在点击添加模式启用时才处理点击事件
      if (!isClickToAddMode.value) {
        return
      }

      // 获取点击位置的地理坐标
      const pickedPosition = viewer.camera.pickEllipsoid(
        event.position,
        viewer.scene.globe.ellipsoid,
      )

      if (pickedPosition) {
        // 将笛卡尔坐标转换为地理坐标
        const cartographic = Cartographic.fromCartesian(pickedPosition)
        const longitude = CesiumMath.toDegrees(cartographic.longitude)
        const latitude = CesiumMath.toDegrees(cartographic.latitude)

        // 根据当前缩放级别计算合理的默认高度
        const defaultHeight = calculateDefaultHeight(viewer)

        console.log(
          `Clicked at: ${longitude.toFixed(6)}, ${latitude.toFixed(6)}, default height: ${defaultHeight}m`,
        )

        // 添加临时标记点显示点击位置
        const entity = viewer.entities.add({
          position: pickedPosition,
          point: {
            pixelSize: 10,
            color: Color.YELLOW,
            outlineColor: Color.BLACK,
            outlineWidth: 2,
            heightReference: HeightReference.CLAMP_TO_GROUND,
          },
        })

        // 显示确认对话框，包含计算出的默认高度信息
        const locationName = prompt(
          `📍 在此位置添加新地点\n\n` +
          `🌍 经度: ${longitude.toFixed(6)}\n` +
          `🌍 纬度: ${latitude.toFixed(6)}\n` +
          `📏 建议高度: ${defaultHeight.toLocaleString()}米\n\n` +
          `请输入地点名称（留空取消）:`,
        )

        // 移除临时标记点
        viewer.entities.remove(entity)

        if (locationName && locationName.trim()) {
          addLocationAtPosition(longitude, latitude, defaultHeight, locationName.trim())
        }
      }
    },
    ScreenSpaceEventType.LEFT_CLICK,
  )

  // 添加鼠标移动事件，显示当前坐标（只在点击添加模式下显示）
  handler.setInputAction(
    (event: ScreenSpaceEventHandler.MotionEvent) => {
      if (!isClickToAddMode.value) {
        mousePosition.value.visible = false
        return
      }

      const pickedPosition = viewer.camera.pickEllipsoid(
        event.endPosition,
        viewer.scene.globe.ellipsoid,
      )

      if (pickedPosition) {
        const cartographic = Cartographic.fromCartesian(pickedPosition)
        const longitude = CesiumMath.toDegrees(cartographic.longitude)
        const latitude = CesiumMath.toDegrees(cartographic.latitude)
        const suggestedHeight = calculateDefaultHeight(viewer)

        mousePosition.value = {
          longitude: Number(longitude.toFixed(6)),
          latitude: Number(latitude.toFixed(6)),
          height: suggestedHeight,
          visible: true,
        }
      } else {
        mousePosition.value.visible = false
      }
    },
    ScreenSpaceEventType.MOUSE_MOVE,
  )
}

// 在指定位置添加地点
const addLocationAtPosition = async (
  longitude: number,
  latitude: number,
  height: number,
  name: string,
) => {
  try {
    console.log(`Adding location: ${name} at ${longitude}, ${latitude}`)

    // 创建新位置（国家和城市将由服务器自动获取）
    const newLocation = await createLocation({
      name,
      longitude,
      latitude,
      height,
      isDefault: false,
      stayDuration: 10, // 默认停留10秒，可根据需要调整
    })

    console.log('Location added successfully:', newLocation)
    alert(`位置 "${name}" 添加成功！\n国家: ${newLocation.country}\n城市: ${newLocation.city}`)
  } catch (error) {
    console.error('Failed to add location:', error)
    alert(`添加位置失败: ${error instanceof Error ? error.message : '未知错误'}`)
  }
}

// 切换点击添加模式
const toggleClickToAddMode = () => {
  isClickToAddMode.value = !isClickToAddMode.value
  console.log('Click to add mode:', isClickToAddMode.value ? 'enabled' : 'disabled')
}

// 加载所有事件
const loadAllEvents = async () => {
  try {
    allEvents.value = await getAllEvents()
    console.log(`Loaded ${allEvents.value.length} events`)
  } catch (error) {
    console.error('Failed to load events:', error)
  }
}


// 显示事件图标
const showEventIconsOnMap = () => {
  if (!cesiumViewer.value) {
    console.error('Cesium viewer not available')
    return
  }

  // 清除现有的事件实体
  clearEventIcons()

  if (!showEventIcons.value) {
    console.log('Event icons are disabled')
    return
  }

  console.log(`Showing ${allEvents.value.length} events on map`)

  // 为每个活跃事件添加图标
  allEvents.value.forEach((event, index) => {
    if (!event.isActive || !event.location) {
      console.log(
        `Skipping event ${event.name}: active=${event.isActive}, hasLocation=${!!event.location}`,
      )
      return
    }

    console.log(
      `Adding icon for event ${index + 1}: ${event.name} at ${event.location.longitude}, ${event.location.latitude}, height: ${event.location.height}`,
    )

    // 使用贴地高度，和播放视图一致
    const position = Cartesian3.fromDegrees(
      event.location.longitude,
      event.location.latitude,
      0, // 贴地显示
    )

    // 使用自定义图标或根据事件类型选择图标和颜色
    let iconText = event.customIcon || '📅'
    let iconColor = event.iconColor || '#9C27B0' // 使用自定义颜色或默认紫色

    // 如果没有自定义图标，则根据媒体类型自动选择
    if (!event.customIcon) {
      if (event.imageUrl && event.videoUrl) {
        iconText = '🎬' // 图片+视频
        iconColor = event.iconColor || '#FF5722' // 橙红色
      } else if (event.videoUrl) {
        iconText = '🎥' // 仅视频
        iconColor = event.iconColor || '#F44336' // 红色
      } else if (event.imageUrl) {
        iconText = '🖼️' // 仅图片
        iconColor = event.iconColor || '#2196F3' // 蓝色
      }
    }

    // 创建图标canvas
    const iconCanvas = createEventIconCanvas(iconText, iconColor)

    try {
      const entity = cesiumViewer.value!.entities.add({
        position: position,
        billboard: {
          image: iconCanvas,
          scale: 1.0,
          verticalOrigin: VerticalOrigin.BOTTOM,
          heightReference: HeightReference.CLAMP_TO_GROUND, // 贴地显示
          disableDepthTestDistance: Number.POSITIVE_INFINITY,
          color: Color.WHITE,
        },
        label: {
          text: event.name,
          font: '14pt serif',
          pixelOffset: new Cartesian2(0, -60),
          fillColor: Color.WHITE,
          outlineColor: Color.BLACK,
          outlineWidth: 2,
          style: LabelStyle.FILL_AND_OUTLINE,
          verticalOrigin: VerticalOrigin.BOTTOM,
          heightReference: HeightReference.CLAMP_TO_GROUND, // 贴地显示
          disableDepthTestDistance: Number.POSITIVE_INFINITY,
          show: false, // 默认不显示标签，鼠标悬停时显示
        },
        description: `
          <div style="font-family: Arial, sans-serif;">
            <h3>${event.name}</h3>
            <p><strong>位置:</strong> ${event.location.name}</p>
            <p><strong>描述:</strong> ${event.description || '无描述'}</p>
            <p><strong>时间:</strong> ${new Date(event.eventTime).toLocaleString()}</p>
            <p><strong>状态:</strong> ${event.isActive ? '活跃' : '非活跃'}</p>
            ${event.imageUrl ? `<p><strong>图片:</strong> <a href="${event.imageUrl}" target="_blank">查看</a></p>` : ''}
            ${event.videoUrl ? `<p><strong>视频:</strong> <a href="${event.videoUrl}" target="_blank">查看</a></p>` : ''}
          </div>
        `,
        // eventData: event, // 存储事件数据以便后续使用
      })

      eventEntities.value.push(entity)
      console.log(`Successfully added entity for event: ${event.name}`)
    } catch (error) {
      console.error(`Failed to add entity for event ${event.name}:`, error)
    }
  })

  console.log(`Added ${eventEntities.value.length} event icons to map`)

  // 强制刷新场景以立即显示图标
  forceSceneRefresh()

  console.log('Scene refreshed to show new icons')

  // 如果是第一次添加图标，可以选择飞到第一个事件位置查看（可选）
  // if (eventEntities.value.length > 0 && allEvents.value.length > 0) {
  //   const firstEvent = allEvents.value.find(e => e.isActive && e.location)
  //   if (firstEvent && firstEvent.location) {
  //     console.log('Flying to first event location for verification')
  //     cesiumViewer.value!.camera.flyTo({
  //       destination: Cartesian3.fromDegrees(
  //         firstEvent.location.longitude,
  //         firstEvent.location.latitude,
  //         firstEvent.location.height * 0.5 // 飞到一半高度查看
  //       ),
  //       duration: 3.0
  //     })
  //   }
  // }
}

// 清除事件图标
const clearEventIcons = () => {
  if (!cesiumViewer.value) return

  console.log(`Clearing ${eventEntities.value.length} event icons`)

  eventEntities.value.forEach((entity) => {
    cesiumViewer.value!.entities.remove(entity)
  })
  eventEntities.value = []

  // 强制刷新场景
  forceSceneRefresh()

  console.log('Event icons cleared and scene refreshed')
}

// 切换事件图标显示
const toggleEventIcons = async () => {
  showEventIcons.value = !showEventIcons.value
  console.log('Toggling event icons:', showEventIcons.value ? 'showing' : 'hiding')
  console.log('Current events count:', allEvents.value.length)
  console.log('Cesium viewer available:', !!cesiumViewer.value)

  if (!cesiumViewer.value) {
    console.error('Cannot toggle icons: Cesium viewer not available')
    return
  }

  if (showEventIcons.value) {
    // 如果事件数据为空，先加载事件
    if (allEvents.value.length === 0) {
      console.log('No events loaded, loading events first...')
      await loadAllEvents()
    }
    showEventIconsOnMap()
  } else {
    clearEventIcons()
  }

  // 强制场景刷新确保立即生效
  forceSceneRefresh()

  // 额外延迟刷新确保完全生效
  setTimeout(() => {
    forceSceneRefresh()
    console.log('Additional scene refresh for immediate effect')
  }, 100)

  console.log('Event icons toggle completed:', showEventIcons.value ? 'shown' : 'hidden')
}

// 强制场景刷新
const forceSceneRefresh = () => {
  if (!cesiumViewer.value) return

  try {
    // 方法1: 基本场景刷新
    if (cesiumViewer.value.scene) {
      cesiumViewer.value.scene.requestRender()
    }

    // 方法2: 微小相机移动触发重绘
    const camera = cesiumViewer.value.camera
    const currentPosition = camera.position.clone()
    const offset = new Cartesian3(0.001, 0.001, 0.001)
    const newPosition = Cartesian3.add(
      currentPosition,
      offset,
      new Cartesian3(),
    )

    camera.setView({
      destination: newPosition,
      orientation: {
        heading: camera.heading,
        pitch: camera.pitch,
        roll: camera.roll,
      },
    })

    // 立即移回原位置
    setTimeout(() => {
      camera.setView({
        destination: currentPosition,
        orientation: {
          heading: camera.heading,
          pitch: camera.pitch,
          roll: camera.roll,
        },
      })
    }, 10)

    // 方法3: 强制实体集合更新
    // if (cesiumViewer.value.entities) {
    //   cesiumViewer.value.entities._suspendEvents = false
    //   cesiumViewer.value.entities._fireChangedEvent()
    // }

    console.log('Scene force refreshed with multiple methods')
  } catch (error) {
    console.warn('Some refresh methods failed, but basic refresh should work:', error)
    // 备用方法：基本刷新
    if (cesiumViewer.value.scene) {
      cesiumViewer.value.scene.requestRender()
    }
  }
}

// 强制刷新事件图标（调试用）
const forceRefreshEventIcons = async () => {
  console.log('Force refreshing event icons...')
  await loadAllEvents()
  console.log('Events loaded:', allEvents.value.length)
  allEvents.value.forEach((event, index) => {
    console.log(`Event ${index + 1}:`, {
      name: event.name,
      isActive: event.isActive,
      hasLocation: !!event.location,
      location: event.location
        ? {
          name: event.location.name,
          longitude: event.location.longitude,
          latitude: event.location.latitude,
          height: event.location.height,
        }
        : null,
    })
  })

  if (cesiumViewer.value) {
    clearEventIcons()
    showEventIconsOnMap()
    forceSceneRefresh()
  } else {
    console.error('Cesium viewer not available for icon refresh')
  }
}

// 设置事件图标点击处理
const setupEventIconClickHandler = (viewer: Viewer) => {


  const handler = new ScreenSpaceEventHandler(viewer.scene.canvas)

  // 鼠标悬停显示标签
  handler.setInputAction((event: ScreenSpaceEventHandler.MotionEvent) => {
    const pickedObject = viewer.scene.pick(event.endPosition)

    // 隐藏所有标签
    eventEntities.value.forEach((entity) => {
      if (entity.label) {
        // entity.label.show = false
      }
    })

    // 显示悬停的实体标签
    if (pickedObject && pickedObject.id && pickedObject.id.eventData) {
      if (pickedObject.id.label) {
        pickedObject.id.label.show = true
      }
      viewer.canvas.style.cursor = 'pointer'
    } else {
      viewer.canvas.style.cursor = 'default'
    }
  }, ScreenSpaceEventType.MOUSE_MOVE)

  // 点击事件图标显示事件详情
  handler.setInputAction((event: ScreenSpaceEventHandler.PositionedEvent) => {
    const pickedObject = viewer.scene.pick(event.position)

    if (pickedObject && pickedObject.id && pickedObject.id.eventData) {
      const eventData = pickedObject.id.eventData
      console.log('Clicked event:', eventData.name)

      // 显示事件详情
      handleShowEvent(eventData)
    }
  }, ScreenSpaceEventType.LEFT_CLICK)
}

onMounted(async () => {
  console.log('EarthView mounted')

  // 暴露调试函数到全局
  // if (typeof window !== 'undefined') {
  //   ; (window as any).debugEventIcons = {
  //     forceRefresh: forceRefreshEventIcons,
  //     toggle: toggleEventIcons,
  //     show: showEventIconsOnMap,
  //     clear: clearEventIcons,
  //     loadEvents: loadAllEvents,
  //     forceSceneRefresh: forceSceneRefresh,
  //     getEvents: () => allEvents.value,
  //     getViewer: () => cesiumViewer.value,
  //     getShowState: () => showEventIcons.value,
  //     getEntitiesCount: () => eventEntities.value.length,
  //   }
  //   console.log('Debug functions exposed to window.debugEventIcons')
  // }

  if (!rootEl.value) {
    console.error('rootEl is not defined')
    return
  }

  const id = rootEl.value.id || 'earthViewId'

  // 应用高分屏优化到容器元素
  highDPIOptimizer.applyToElement(rootEl.value)

  // 打印高分屏检测信息
  highDPIOptimizer.logInfo()

  // 初始化Cesium并保存viewer实例
  if (window.initCesium) {
    cesiumViewer.value = window.initCesium(id)
  }

  // 添加地球点击事件监听
  if (cesiumViewer.value) {
    setupEarthClickHandler(cesiumViewer.value)
    setupEventIconClickHandler(cesiumViewer.value)
  }

  // 加载事件数据并显示图标
  await loadAllEvents()

  // 等待Cesium完全初始化后再显示图标
  if (cesiumViewer.value) {
    // 使用setTimeout确保Cesium完全初始化
    setTimeout(() => {
      console.log('Attempting to show event icons after Cesium initialization')
      showEventIconsOnMap()
    }, 1000)
  }

  // 获取默认位置并旋转到该位置
  try {
    const defaultLocation = await getDefaultLocation()
    console.log('Default location loaded:', defaultLocation)

    if (cesiumViewer.value) {
      // 使用flyTo方法平滑旋转到默认位置
      cesiumViewer.value.camera.flyTo({
        destination: Cartesian3.fromDegrees(
          defaultLocation.longitude,
          defaultLocation.latitude,
          defaultLocation.height,
        ),
        orientation: {
          heading: CesiumMath.toRadians(0),
          pitch: CesiumMath.toRadians(-90),
          roll: CesiumMath.toRadians(0),
        },
        duration: 3.0, // 动画持续时间（秒）
        complete: () => {
          console.log(
            `Successfully rotated to default location: ${defaultLocation.longitude}, ${defaultLocation.latitude}`,
          )
        },
      })
    }
  } catch (error) {
    console.error('Failed to load default location:', error)
  }

  // 加载位置数据
  await loadLocations()
})
</script>

<template>
  <div class="earth-view-container">
    <div ref="rootEl" id="earthViewId"></div>
    <EarthRotationControl :viewer="cesiumViewer" @open-location-modal="openLocationModal"
      @open-animation-modal="openAnimationModal" @open-event-modal="openEventModal"
      @toggle-click-mode="toggleClickToAddMode" @toggle-event-icons="toggleEventIcons"
      @debug-event-icons="forceRefreshEventIcons" />
    <LocationManagementModal v-model:visible="showLocationModal" @location-changed="handleLocationChanged"
      @jump-to-location="handleJumpToLocation" />
    <AnimationManagementModal v-model:visible="showAnimationModal" :viewer="cesiumViewer"
      @animation-changed="handleAnimationChanged" @show-location-events="handleShowLocationEvents" />
    <EventManagementModal v-model:visible="showEventModal" @event-changed="handleEventChanged"
      @show-event="handleShowEvent" />
    <EventDisplayModal v-model:visible="showEventDisplay" :event="currentEvent" />
    <EventSelectionModal v-model:visible="showEventSelection" :events="locationEvents"
      :location-name="currentLocationName" :auto-select-delay="8000" @event-selected="handleEventSelected"
      @skip="handleSkipEvents" />

    <!-- 鼠标位置坐标显示（点击添加模式下） -->
    <div v-if="mousePosition.visible && isClickToAddMode" class="coordinate-display">
      <span class="coordinate-text">
        📍 {{ mousePosition.longitude }}, {{ mousePosition.latitude }}
      </span>
      <span class="height-text"> 📏 建议高度: {{ mousePosition.height.toLocaleString() }}m </span>
      <span class="click-hint">点击添加位置</span>
    </div>
  </div>
</template>

<style>
.earth-view-container {
  width: 100vw;
  height: 100vh;
  position: fixed;
  top: 0;
  left: 0;
  margin: 0;
  padding: 0;
  overflow: hidden;
  z-index: 1;
}

#earthViewId {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  margin: 0;
  padding: 0;
  border: none;
  outline: none;
  /* 高分屏优化 */
  image-rendering: -webkit-optimize-contrast;
  image-rendering: crisp-edges;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  /* 防止高分屏下的模糊 */
  transform: translateZ(0);
  backface-visibility: hidden;
  perspective: 1000px;
}

.coordinate-display {
  position: fixed;
  bottom: 20px;
  left: 20px;
  background: rgba(42, 42, 42, 0.9);
  color: white;
  padding: 8px 12px;
  border-radius: 8px;
  font-family: 'Courier New', monospace;
  font-size: 12px;
  z-index: 1000;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.coordinate-text {
  font-weight: 600;
  color: #4caf50;
}

.height-text {
  font-weight: 500;
  color: #2196f3;
  font-size: 11px;
}

.click-hint {
  font-size: 10px;
  color: #ccc;
  opacity: 0.8;
}

/* 高分屏下的额外优化 */
@media (-webkit-min-device-pixel-ratio: 2),
(min-resolution: 192dpi) {
  #earthViewId {
    /* 确保在高分屏下清晰显示 */
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
  }
}

/* 4K及以上屏幕的优化 */
@media (-webkit-min-device-pixel-ratio: 3),
(min-resolution: 288dpi) {
  #earthViewId {
    /* 超高分屏的额外优化 */
    image-rendering: pixelated;
  }
}
</style>
