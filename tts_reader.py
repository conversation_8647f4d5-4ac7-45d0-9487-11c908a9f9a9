import argparse
from TTS.api import TTS
import os

def main():
    parser = argparse.ArgumentParser(description="Read a text file and generate speech using Mozilla TTS.")
    parser.add_argument("--input", "-i", required=True, help="Path to the input .txt file.")
    parser.add_argument("--output", "-o", required=True, help="Path to the output .wav file.")
    parser.add_argument("--model", "-m", default="tts_models/zh-CN/baker/tacotron2-DDC-GST", help="TTS model name.")

    args = parser.parse_args()

    if not os.path.exists(args.input):
        print(f"❌ Input file '{args.input}' does not exist.")
        return

    with open(args.input, "r", encoding="utf-8") as f:
        text = f.read()

    print(f"📖 Reading from: {args.input}")
    print(f"📤 Saving to: {args.output}")
    print(f"🧠 Using model: {args.model}")

    tts = TTS(model_name=args.model, progress_bar=False)
    tts.tts_to_file(text=text, file_path=args.output)

    print("✅ Speech synthesis complete.")

if __name__ == "__main__":
    main()
