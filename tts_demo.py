import torch

# 解决 PyTorch 2.6+ 的安全反序列化问题
try:
    from TTS.utils.radam import RAdam
    torch.serialization.add_safe_globals({"TTS.utils.radam.RAdam": RAdam})
except Exception:
    pass

# 导入 TTS 接口
from TTS.api import TTS

# 使用中文模型，显式指定使用 CPU（兼容性更好）
tts = TTS(model_name="tts_models/zh-CN/baker/tacotron2-DDC-GST", progress_bar=True, gpu=False)

# 合成语音并保存为 WAV 文件
text = "你好，欢迎使用 Mozilla TTS。"
output_path = "demo.wav"
tts.tts_to_file(text=text, file_path=output_path)
