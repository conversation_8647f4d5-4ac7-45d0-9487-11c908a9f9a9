from TTS.api import TTS

# 加载支持多说话人的模型（例如 VCTK 数据集）
tts = TTS(model_name="tts_models/en/vctk/vits", progress_bar=False, gpu=False)

# 查看可用说话人列表
speakers = tts.speakers
print("可用说话人:", speakers)

# 指定不同的说话人生成语音
sentences = [
    ("p226", "Hello, this is speaker 226 speaking."),
    ("p270", "And this is speaker 270 responding."),
    ("p286", "Finally, speaker 286 joins the conversation.")
]

for speaker, text in sentences:
    file_name = f"{speaker}.wav"
    print(f"合成语音: {speaker} -> {file_name}")
    tts.tts_to_file(text=text, speaker=speaker, file_path=file_name)
