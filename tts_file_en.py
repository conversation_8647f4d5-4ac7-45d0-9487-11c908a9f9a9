from TTS.api import TTS

# 使用的模型名称（这里用的是一个英文模型）
# model_name = "tts_models/en/ljspeech/tacotron2-DDC"
model_name="tts_models/en/ljspeech/tacotron2-DDC-GST"


# 创建 TTS 实例
tts = TTS(model_name=model_name, progress_bar=False)

# 读取文本文件
with open("input-en.txt", "r", encoding="utf-8") as f:
    text = f.read()

# 生成语音并保存为文件
tts.tts_to_file(text=text, file_path="output-en.wav")

print("语音合成完成，保存为 output.wav")
