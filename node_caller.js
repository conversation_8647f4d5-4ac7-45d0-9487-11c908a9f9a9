const { execFile } = require('child_process');
const path = require('path');

const pythonScript = path.join(__dirname, 'tts_reader.py');
const inputFile = path.join(__dirname, 'input.txt');
const outputFile = path.join(__dirname, 'output.wav');

execFile('python3', [pythonScript, '--input', inputFile, '--output', outputFile], (error, stdout, stderr) => {
  if (error) {
    console.error(`❌ Error: ${error.message}`);
    return;
  }
  if (stderr) {
    console.error(`⚠️ Stderr: ${stderr}`);
    return;
  }
  console.log(`✅ Output:\n${stdout}`);
});
